'use client';

import { useState, useEffect } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { billFormSchema, BillFormData } from '@/lib/validation/billing-schemas';
import { FormValidator, ValidationResult } from '@/lib/validation/validation-utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Separator } from '@/components/ui/separator';
import { 
  IconPlus, 
  IconTrash, 
  IconReceipt,
  IconX,
  IconCalculator,
  IconUser,
  IconCalendar
} from '@tabler/icons-react';
import { Bill, Patient, Appointment, Treatment, BillItem } from '@/types/clinic';
import { billsAPI, BillingAPIError, billingUtils } from '@/lib/api/billing';
import { toast } from 'sonner';
import { billingNotifications } from '@/lib/billing-notifications';

// Remove the local schemas since we're now using the centralized ones

interface BillFormProps {
  bill?: Bill;
  patients?: Patient[];
  appointments?: Appointment[];
  treatments?: Treatment[];
  onSuccess?: (bill: Bill) => void;
  onCancel?: () => void;
  isOpen?: boolean;
}

// Bill type options
const billTypes = [
  { value: 'treatment', label: '治疗账单' },
  { value: 'consultation', label: '咨询账单' },
  { value: 'deposit', label: '押金账单' },
  { value: 'additional', label: '补充账单' },
];

// Item type options
const itemTypes = [
  { value: 'treatment', label: '治疗项目' },
  { value: 'consultation', label: '咨询服务' },
  { value: 'material', label: '材料费用' },
  { value: 'service', label: '其他服务' },
];

export function BillForm({
  bill,
  patients = [],
  appointments = [],
  treatments = [],
  onSuccess,
  onCancel,
  isOpen = true
}: BillFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [calculatedTotals, setCalculatedTotals] = useState({
    subtotal: 0,
    totalAmount: 0,
  });
  const [formValidator] = useState(() => new FormValidator(billFormSchema));
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  const isEditing = !!bill;

  const form = useForm<BillFormData>({
    resolver: zodResolver(billFormSchema),
    defaultValues: {
      patient: bill?.patientId ? String(bill.patientId) : '',
      appointment: bill?.appointmentId ? String(bill.appointmentId) : '',
      treatment: bill?.treatmentId ? String(bill.treatmentId) : '',
      billType: bill?.billType || 'treatment',
      description: bill?.description || '',
      notes: bill?.notes || '',
      dueDate: bill?.dueDate ? new Date(bill.dueDate).toISOString().split('T')[0] : '',
      discountAmount: bill?.discountAmount || 0,
      taxAmount: bill?.taxAmount || 0,
      items: bill?.items?.map(item => ({
        itemType: item.itemType,
        itemName: item.itemName,
        description: item.description || '',
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        discountRate: item.discountRate || 0,
      })) || [
        {
          itemType: 'treatment' as const,
          itemName: '',
          description: '',
          quantity: 1,
          unitPrice: 0,
          discountRate: 0,
        }
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'items',
  });

  // Watch form values for real-time calculation
  const watchedItems = form.watch('items');
  const watchedDiscountAmount = form.watch('discountAmount');
  const watchedTaxAmount = form.watch('taxAmount');

  // Calculate totals whenever items change
  useEffect(() => {
    const subtotal = watchedItems.reduce((sum, item) => {
      const itemTotal = (item.quantity || 0) * (item.unitPrice || 0);
      const itemDiscount = itemTotal * ((item.discountRate || 0) / 100);
      return sum + (itemTotal - itemDiscount);
    }, 0);

    const totalAmount = billingUtils.calculateBillTotal(
      subtotal,
      watchedDiscountAmount || 0,
      watchedTaxAmount || 0
    );

    setCalculatedTotals({ subtotal, totalAmount });
  }, [watchedItems, watchedDiscountAmount, watchedTaxAmount]);

  const onSubmit = async (data: BillFormData) => {
    try {
      setIsSubmitting(true);

      const billData = {
        patient: parseInt(data.patient),
        appointment: data.appointment && data.appointment !== 'none' ? parseInt(data.appointment) : undefined,
        treatment: data.treatment && data.treatment !== 'none' ? parseInt(data.treatment) : undefined,
        billType: data.billType,
        subtotal: calculatedTotals.subtotal,
        discountAmount: data.discountAmount || 0,
        taxAmount: data.taxAmount || 0,
        totalAmount: calculatedTotals.totalAmount,
        description: data.description,
        notes: data.notes || undefined,
        dueDate: data.dueDate,
        items: data.items.map(item => ({
          itemType: item.itemType,
          itemName: item.itemName,
          description: item.description || undefined,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          discountRate: item.discountRate || 0,
        })),
      };

      let result: Bill;
      if (isEditing && bill) {
        result = await billsAPI.updateBill(bill.id, billData);
        billingNotifications.bill.updated(result);
      } else {
        result = await billsAPI.createBill(billData);
        billingNotifications.bill.created(result);
      }

      if (onSuccess) {
        onSuccess(result);
      }

      if (!isEditing) {
        form.reset();
      }
      
    } catch (error) {
      console.error('Bill operation failed:', error);
      const errorMessage = error instanceof BillingAPIError
        ? error.message
        : undefined;

      if (isEditing) {
        billingNotifications.bill.updateError(errorMessage);
      } else {
        billingNotifications.bill.createError(errorMessage);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const addItem = () => {
    append({
      itemType: 'treatment',
      itemName: '',
      description: '',
      quantity: 1,
      unitPrice: 0,
      discountRate: 0,
    });
  };

  const removeItem = (index: number) => {
    if (fields.length > 1) {
      remove(index);
    } else {
      billingNotifications.validation.confirmAction('至少需要保留一个账单项目');
    }
  };

  if (!isOpen) {
    return null;
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <IconReceipt className="h-5 w-5" />
              {isEditing ? '编辑账单' : '创建账单'}
            </CardTitle>
            <CardDescription>
              {isEditing ? `编辑账单 ${bill?.billNumber}` : '创建新的账单'}
            </CardDescription>
          </div>
          {onCancel && (
            <Button variant="ghost" size="sm" onClick={onCancel}>
              <IconX className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Patient Selection */}
              <FormField
                control={form.control}
                name="patient"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <IconUser className="h-4 w-4" />
                      患者
                    </FormLabel>
                    <FormControl>
                      <Select value={field.value} onValueChange={field.onChange}>
                        <SelectTrigger>
                          <SelectValue placeholder="选择患者" />
                        </SelectTrigger>
                        <SelectContent>
                          {patients.map((patient) => (
                            <SelectItem key={patient.id} value={String(patient.id)}>
                              {patient.fullName} - {patient.phone}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Bill Type */}
              <FormField
                control={form.control}
                name="billType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>账单类型</FormLabel>
                    <FormControl>
                      <Select value={field.value} onValueChange={field.onChange}>
                        <SelectTrigger>
                          <SelectValue placeholder="选择账单类型" />
                        </SelectTrigger>
                        <SelectContent>
                          {billTypes.map((type) => (
                            <SelectItem key={type.value} value={type.value}>
                              {type.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Appointment (Optional) */}
              <FormField
                control={form.control}
                name="appointment"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>关联预约 (可选)</FormLabel>
                    <FormControl>
                      <Select value={field.value || ''} onValueChange={field.onChange}>
                        <SelectTrigger>
                          <SelectValue placeholder="选择预约" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">无关联预约</SelectItem>
                          {appointments.map((appointment) => (
                            <SelectItem key={appointment.id} value={appointment.id}>
                              {new Date(appointment.appointmentDate).toLocaleDateString('zh-CN')} - 
                              {typeof appointment.treatment === 'object' 
                                ? appointment.treatment.name 
                                : '未知治疗'}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Due Date */}
              <FormField
                control={form.control}
                name="dueDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <IconCalendar className="h-4 w-4" />
                      到期日期
                    </FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Description */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>账单描述</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="输入账单描述..."
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>备注 (可选)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="账单相关备注..."
                      className="resize-none"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Separator />

            {/* Bill Items Section */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">账单项目</h3>
                <Button type="button" variant="outline" size="sm" onClick={addItem}>
                  <IconPlus className="h-4 w-4 mr-2" />
                  添加项目
                </Button>
              </div>

              {/* Bill Items List */}
              <div className="space-y-4">
                {fields.map((field, index) => (
                  <Card key={field.id} className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-6 gap-4 items-end">
                      {/* Item Type */}
                      <FormField
                        control={form.control}
                        name={`items.${index}.itemType`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>类型</FormLabel>
                            <FormControl>
                              <Select value={field.value} onValueChange={field.onChange}>
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  {itemTypes.map((type) => (
                                    <SelectItem key={type.value} value={type.value}>
                                      {type.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Item Name */}
                      <FormField
                        control={form.control}
                        name={`items.${index}.itemName`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>项目名称</FormLabel>
                            <FormControl>
                              <Input placeholder="项目名称" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Quantity */}
                      <FormField
                        control={form.control}
                        name={`items.${index}.quantity`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>数量</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                step="0.01"
                                min="0.01"
                                placeholder="1"
                                {...field}
                                onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Unit Price */}
                      <FormField
                        control={form.control}
                        name={`items.${index}.unitPrice`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>单价</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                step="0.01"
                                min="0"
                                placeholder="0.00"
                                {...field}
                                onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Discount Rate */}
                      <FormField
                        control={form.control}
                        name={`items.${index}.discountRate`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>折扣率 (%)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                step="0.1"
                                min="0"
                                max="100"
                                placeholder="0"
                                {...field}
                                onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Remove Button */}
                      <div className="flex items-center">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => removeItem(index)}
                          disabled={fields.length <= 1}
                        >
                          <IconTrash className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Item Description */}
                    <div className="mt-4">
                      <FormField
                        control={form.control}
                        name={`items.${index}.description`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>项目描述 (可选)</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="项目详细描述..."
                                className="resize-none"
                                rows={2}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Item Total Display */}
                    <div className="mt-2 text-right">
                      <span className="text-sm text-muted-foreground">
                        小计: {billingUtils.formatCurrency(
                          (() => {
                            const item = watchedItems[index];
                            if (!item) return 0;
                            const itemTotal = (item.quantity || 0) * (item.unitPrice || 0);
                            const itemDiscount = itemTotal * ((item.discountRate || 0) / 100);
                            return itemTotal - itemDiscount;
                          })()
                        )}
                      </span>
                    </div>
                  </Card>
                ))}
              </div>
            </div>

            <Separator />

            {/* Totals Section */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Discount Amount */}
              <FormField
                control={form.control}
                name="discountAmount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>额外折扣金额</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="0.00"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormDescription>
                      在项目折扣基础上的额外折扣
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Tax Amount */}
              <FormField
                control={form.control}
                name="taxAmount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>税费金额</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="0.00"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormDescription>
                      需要添加的税费金额
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Total Display */}
              <div className="space-y-2">
                <Label>账单总计</Label>
                <div className="bg-muted/50 rounded-lg p-3 space-y-1">
                  <div className="flex justify-between text-sm">
                    <span>项目小计:</span>
                    <span>{billingUtils.formatCurrency(calculatedTotals.subtotal)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>额外折扣:</span>
                    <span>-{billingUtils.formatCurrency(watchedDiscountAmount || 0)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>税费:</span>
                    <span>+{billingUtils.formatCurrency(watchedTaxAmount || 0)}</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between font-semibold">
                    <span>总金额:</span>
                    <span className="text-lg">
                      {billingUtils.formatCurrency(calculatedTotals.totalAmount)}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3 pt-4">
              <Button
                type="submit"
                disabled={isSubmitting}
                className="flex-1"
              >
                {isSubmitting
                  ? (isEditing ? '更新中...' : '创建中...')
                  : (isEditing ? '更新账单' : '创建账单')
                }
              </Button>
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isSubmitting}
                >
                  取消
                </Button>
              )}
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
