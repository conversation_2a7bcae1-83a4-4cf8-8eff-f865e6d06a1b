import { NextRequest, NextResponse } from 'next/server'
import { authenticateWithPayload, makeAuthenticatedPayloadRequest } from '../../../../lib/payload-auth-middleware'

/**
 * POST /api/deposits/refund - Process deposit refund
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check permissions - only admin can process refunds
    if (authContext.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Only administrators can process deposit refunds' },
        { status: 403 }
      );
    }

    // Parse request body
    const { depositId, refundAmount, refundReason, refundMethod = 'cash' } = await request.json();

    if (!depositId || !refundAmount || !refundReason) {
      return NextResponse.json(
        { error: 'Deposit ID, refund amount, and refund reason are required' },
        { status: 400 }
      );
    }

    if (refundAmount <= 0) {
      return NextResponse.json(
        { error: 'Refund amount must be positive' },
        { status: 400 }
      );
    }

    // Get deposit details
    const deposit = await makeAuthenticatedPayloadRequest(
      authContext,
      'deposits',
      'findByID',
      {
        id: depositId,
        depth: 2,
      }
    ) as any;

    if (!deposit) {
      return NextResponse.json(
        { error: 'Deposit not found' },
        { status: 404 }
      );
    }

    // Validate refund amount
    const availableForRefund = (deposit as any).remainingAmount || ((deposit as any).amount - ((deposit as any).usedAmount || 0));
    
    if (refundAmount > availableForRefund) {
      return NextResponse.json(
        { error: `Refund amount cannot exceed available balance of ${availableForRefund}` },
        { status: 400 }
      );
    }

    // Check if deposit is in a refundable state
    if (deposit.status === 'refunded') {
      return NextResponse.json(
        { error: 'Deposit has already been refunded' },
        { status: 400 }
      );
    }

    if (deposit.status === 'expired') {
      return NextResponse.json(
        { error: 'Cannot refund expired deposit without admin approval' },
        { status: 400 }
      );
    }

    // Create refund payment record (negative payment)
    const refundPayment = await makeAuthenticatedPayloadRequest(
      authContext,
      'payments',
      'create',
      {
        data: {
          paymentMethod: refundMethod,
          amount: -refundAmount, // Negative amount for refund
          paymentStatus: 'completed',
          paymentDate: new Date().toISOString(),
          patient: deposit.patient,
          receivedBy: authContext.user.id,
          notes: `Deposit refund: ${refundReason}`,
          transactionId: `REFUND-${Date.now()}`,
          // Link to deposit instead of bill for refunds
          relatedDeposit: depositId,
        }
      }
    );

    // Update deposit status and amounts
    const newUsedAmount = (deposit.usedAmount || 0) + refundAmount;
    const newRemainingAmount = deposit.amount - newUsedAmount;
    const newStatus = newRemainingAmount <= 0 ? 'refunded' : deposit.status;

    const updatedDeposit = await makeAuthenticatedPayloadRequest(
      authContext,
      'deposits',
      'update',
      {
        id: depositId,
        data: {
          usedAmount: newUsedAmount,
          remainingAmount: newRemainingAmount,
          status: newStatus,
          notes: deposit.notes ? 
            `${deposit.notes}\n\nRefund processed: ${refundAmount} (${refundReason})` :
            `Refund processed: ${refundAmount} (${refundReason})`,
        }
      }
    );

    // Generate refund receipt data
    const refundReceipt = {
      refundNumber: `REF-${new Date().toISOString().slice(0, 10).replace(/-/g, '')}-${Date.now().toString().slice(-6)}`,
      depositNumber: deposit.depositNumber,
      originalAmount: deposit.amount,
      refundAmount,
      refundMethod,
      refundReason,
      refundDate: new Date().toISOString(),
      patient: deposit.patient,
      processedBy: {
        firstName: (authContext.user as any).firstName,
        lastName: (authContext.user as any).lastName,
        email: (authContext.user as any).email,
      },
    };

    return NextResponse.json({
      success: true,
      message: 'Deposit refund processed successfully',
      refund: {
        id: (refundPayment as any).id,
        paymentNumber: (refundPayment as any).paymentNumber,
        amount: refundAmount,
        method: refundMethod,
        reason: refundReason,
        processedAt: new Date().toISOString(),
      },
      deposit: updatedDeposit,
      receipt: refundReceipt,
    });

  } catch (error) {
    console.error('Error processing deposit refund:', error);
    return NextResponse.json(
      { error: 'Failed to process deposit refund' },
      { status: 500 }
    );
  }
}
