import { NextRequest, NextResponse } from 'next/server'
import { authenticateWithPayload, makeAuthenticatedPayloadRequest } from '../../../../lib/payload-auth-middleware'

/**
 * GET /api/reports/daily-revenue - Generate daily revenue report
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check permissions - only admin and front-desk can view financial reports
    if (!['admin', 'front-desk'].includes(authContext.user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view financial reports' },
        { status: 403 }
      );
    }

    const url = new URL(request.url);
    const dateParam = url.searchParams.get('date');
    
    if (!dateParam) {
      return NextResponse.json(
        { error: 'Date parameter is required (YYYY-MM-DD format)' },
        { status: 400 }
      );
    }

    // Parse and validate date
    const targetDate = new Date(dateParam);
    if (isNaN(targetDate.getTime())) {
      return NextResponse.json(
        { error: 'Invalid date format. Use YYYY-MM-DD' },
        { status: 400 }
      );
    }

    // Set date range for the specific day
    const startOfDay = new Date(targetDate);
    startOfDay.setHours(0, 0, 0, 0);
    
    const endOfDay = new Date(targetDate);
    endOfDay.setHours(23, 59, 59, 999);

    const dateFilter = {
      paymentDate: {
        greater_than_equal: startOfDay.toISOString(),
        less_than_equal: endOfDay.toISOString(),
      },
      paymentStatus: {
        equals: 'completed'
      }
    };

    // Get payments for the day
    const payments = await makeAuthenticatedPayloadRequest(
      authContext,
      'payments',
      'find',
      {
        where: dateFilter,
        limit: 1000,
        depth: 2,
      }
    );

    // Calculate metrics
    const totalRevenue = (payments as any).docs.reduce((sum: number, payment: any) => sum + payment.amount, 0);
    const paymentCount = (payments as any).totalDocs;

    // Group by payment method
    const paymentMethodBreakdown: Record<string, { amount: number; count: number }> = {};

    (payments as any).docs.forEach((payment: any) => {
      const method = payment.paymentMethod || 'unknown';
      if (!paymentMethodBreakdown[method]) {
        paymentMethodBreakdown[method] = { amount: 0, count: 0 };
      }
      paymentMethodBreakdown[method].amount += payment.amount;
      paymentMethodBreakdown[method].count += 1;
    });

    const reportData = {
      date: dateParam,
      totalRevenue,
      paymentCount,
      paymentMethods: paymentMethodBreakdown,
      generatedAt: new Date().toISOString(),
      generatedBy: {
        firstName: (authContext.user as any).firstName,
        lastName: (authContext.user as any).lastName,
        email: (authContext.user as any).email,
      },
    };

    return NextResponse.json({
      success: true,
      ...reportData,
    });

  } catch (error) {
    console.error('Error generating daily revenue report:', error);
    return NextResponse.json(
      { error: 'Failed to generate daily revenue report' },
      { status: 500 }
    );
  }
}
