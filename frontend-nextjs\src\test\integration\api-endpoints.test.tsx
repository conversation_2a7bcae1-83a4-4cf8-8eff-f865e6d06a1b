import { describe, it, expect, beforeEach, vi } from 'vitest'
import { appointmentsApi, patientsApi, treatmentsApi } from '@/lib/api'

// Mock fetch globally
const mockFetch = vi.fn()
global.fetch = mockFetch as any

describe('API Endpoints Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockFetch.mockClear()
  })

  describe('Appointments API', () => {
    it('should fetch appointments successfully', async () => {
      const mockAppointments = [
        {
          id: '1',
          patientId: 'patient-1',
          treatmentId: 'treatment-1',
          dateTime: '2024-01-15T10:00:00Z',
          status: 'scheduled',
          price: 100,
          duration: 60,
          notes: 'Test appointment'
        }
      ]

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ docs: mockAppointments })
      })

      const result = await appointmentsApi.getAll()

      expect(fetch).toHaveBeenCalledWith('/api/appointments', expect.any(Object))
      expect(result).toEqual(mockAppointments)
    })

    it('should create appointment successfully', async () => {
      const newAppointment = {
        patientId: 'patient-1',
        treatmentId: 'treatment-1',
        dateTime: '2024-01-15T10:00:00Z',
        status: 'scheduled' as const,
        price: 100,
        duration: 60,
        notes: 'Test appointment'
      }

      const mockResponse = { id: '1', ...newAppointment }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      })

      const result = await appointmentsApi.create(newAppointment)

      expect(mockFetch).toHaveBeenCalledWith('/api/appointments', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newAppointment)
      })
      expect(result).toEqual(mockResponse)
    })

    it('should handle API errors gracefully', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error'
      })

      await expect(appointmentsApi.getAll()).rejects.toThrow('Failed to fetch appointments')
    })
  })

  describe('Patients API', () => {
    it('should fetch patients successfully', async () => {
      const mockPatients = [
        {
          id: '1',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '**********',
          dateOfBirth: '1990-01-01',
          address: '123 Main St',
          emergencyContact: 'Jane Doe - **********'
        }
      ]

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ docs: mockPatients })
      })

      const result = await patientsApi.getAll()

      expect(mockFetch).toHaveBeenCalledWith('/api/patients', expect.any(Object))
      expect(result).toEqual(mockPatients)
    })

    it('should create patient successfully', async () => {
      const newPatient = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '**********',
        dateOfBirth: '1990-01-01',
        address: '123 Main St',
        emergencyContact: 'Jane Doe - **********'
      }

      const mockResponse = { id: '1', ...newPatient }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      })

      const result = await patientsApi.create(newPatient)

      expect(mockFetch).toHaveBeenCalledWith('/api/patients', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newPatient)
      })
      expect(result).toEqual(mockResponse)
    })
  })

  describe('Treatments API', () => {
    it('should fetch treatments successfully', async () => {
      const mockTreatments = [
        {
          id: '1',
          name: 'General Consultation',
          description: 'Standard medical consultation',
          defaultPrice: 100,
          defaultDuration: 30,
          category: 'consultation'
        }
      ]

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ docs: mockTreatments })
      })

      const result = await treatmentsApi.getAll()

      expect(mockFetch).toHaveBeenCalledWith('/api/treatments', expect.any(Object))
      expect(result).toEqual(mockTreatments)
    })

    it('should create treatment successfully', async () => {
      const newTreatment = {
        name: 'General Consultation',
        description: 'Standard medical consultation',
        defaultPrice: 100,
        defaultDuration: 30,
        category: 'consultation'
      }

      const mockResponse = { id: '1', ...newTreatment }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      })

      const result = await treatmentsApi.create(newTreatment)

      expect(mockFetch).toHaveBeenCalledWith('/api/treatments', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newTreatment)
      })
      expect(result).toEqual(mockResponse)
    })
  })

  describe('Error Handling', () => {
    it('should handle network errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'))

      await expect(appointmentsApi.getAll()).rejects.toThrow('Network error')
    })

    it('should handle 404 errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: 'Not Found'
      })

      await expect(appointmentsApi.getById('nonexistent')).rejects.toThrow('Failed to fetch appointment')
    })

    it('should handle 401 unauthorized errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized'
      })

      await expect(appointmentsApi.getAll()).rejects.toThrow('Failed to fetch appointments')
    })
  })
})
