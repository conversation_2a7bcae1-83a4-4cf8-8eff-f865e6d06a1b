import { NextRequest, NextResponse } from 'next/server'
import { authenticateWithPayload, makeAuthenticatedPayloadRequest } from '../../../../../lib/payload-auth-middleware'

/**
 * GET /api/payments/[id]/receipt - Generate receipt for a payment
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params before accessing properties
    const { id } = await params;

    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get payment details with related data
    const payment = await makeAuthenticatedPayloadRequest(
      authContext,
      'payments',
      'findByID',
      {
        id,
        depth: 3, // Include bill, patient, and user details
      }
    );

    if (!payment) {
      return NextResponse.json(
        { error: 'Payment not found' },
        { status: 404 }
      );
    }

    // Only generate receipt for completed payments
    if ((payment as any).paymentStatus !== 'completed') {
      return NextResponse.json(
        { error: 'Receipt can only be generated for completed payments' },
        { status: 400 }
      );
    }

    // Generate receipt data
    const receiptData = {
      receiptNumber: (payment as any).receiptNumber,
      paymentNumber: (payment as any).paymentNumber,
      paymentDate: (payment as any).paymentDate,
      amount: (payment as any).amount,
      paymentMethod: (payment as any).paymentMethod,

      // Bill information
      bill: {
        billNumber: (payment as any).bill?.billNumber,
        description: (payment as any).bill?.description,
        totalAmount: (payment as any).bill?.totalAmount,
        remainingAmount: (payment as any).bill?.remainingAmount,
      },

      // Patient information
      patient: {
        fullName: (payment as any).patient?.fullName,
        phone: (payment as any).patient?.phone,
        email: (payment as any).patient?.email,
      },

      // Staff information
      receivedBy: {
        firstName: (payment as any).receivedBy?.firstName,
        lastName: (payment as any).receivedBy?.lastName,
        email: (payment as any).receivedBy?.email,
      },
      
      // Clinic information (you can customize this)
      clinic: {
        name: '北海岸医美诊所',
        address: '诊所地址',
        phone: '诊所电话',
        email: '<EMAIL>',
      },
      
      notes: (payment as any).notes,
      transactionId: (payment as any).transactionId,

      // Related deposit info if applicable
      relatedDeposit: (payment as any).relatedDeposit ? {
        depositNumber: (payment as any).relatedDeposit?.depositNumber,
        depositType: (payment as any).relatedDeposit?.depositType,
      } : null,
      
      // Generated timestamp
      generatedAt: new Date().toISOString(),
      generatedBy: {
        firstName: (authContext.user as any).firstName,
        lastName: (authContext.user as any).lastName,
        email: (authContext.user as any).email,
      },
    };

    return NextResponse.json({
      success: true,
      receipt: receiptData,
    });

  } catch (error) {
    console.error('Error generating receipt:', error);
    return NextResponse.json(
      { error: 'Failed to generate receipt' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/payments/[id]/receipt - Regenerate receipt number for a payment
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params before accessing properties
    const { id } = await params;

    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user has admin role for regenerating receipts
    if (authContext.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Only administrators can regenerate receipts' },
        { status: 403 }
      );
    }

    // Generate new receipt number
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const timestamp = now.getTime().toString().slice(-6);
    const newReceiptNumber = `REC-${year}${month}${day}-${timestamp}`;

    // Update payment with new receipt number
    const updatedPayment = await makeAuthenticatedPayloadRequest(
      authContext,
      'payments',
      'update',
      {
        id,
        data: {
          receiptNumber: newReceiptNumber,
        }
      }
    );

    return NextResponse.json({
      success: true,
      message: 'Receipt number regenerated successfully',
      receiptNumber: newReceiptNumber,
      payment: updatedPayment,
    });

  } catch (error) {
    console.error('Error regenerating receipt:', error);
    return NextResponse.json(
      { error: 'Failed to regenerate receipt' },
      { status: 500 }
    );
  }
}
