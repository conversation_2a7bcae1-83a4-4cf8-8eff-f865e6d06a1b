[{"C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(frontend)\\layout.tsx": "1", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(frontend)\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\admin\\importMap.js": "3", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\admin\\[[...segments]]\\not-found.tsx": "4", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\admin\\[[...segments]]\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\api\\graphql\\route.ts": "6", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\api\\graphql-playground\\route.ts": "7", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\api\\[...slug]\\route.ts": "8", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\layout.tsx": "9", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\appointments\\route.ts": "10", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\appointments\\[id]\\route.ts": "11", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\appointments-direct\\route.ts": "12", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bill-items\\route.ts": "13", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bill-items\\[id]\\route.ts": "14", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bills\\generate-from-appointment\\route.ts": "15", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bills\\route.ts": "16", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bills\\[id]\\route.ts": "17", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\db-test\\route.ts": "18", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\deposits\\apply-to-bill\\route.ts": "19", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\deposits\\refund\\route.ts": "20", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\deposits\\route.ts": "21", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\deposits\\[id]\\route.ts": "22", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\patients\\convert-to-patient\\route.ts": "23", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\patients\\route.ts": "24", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\patients\\[id]\\route.ts": "25", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\payments\\route.ts": "26", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\payments\\[id]\\receipt\\route.ts": "27", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\payments\\[id]\\route.ts": "28", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reports\\daily-revenue\\route.ts": "29", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reports\\financial\\route.ts": "30", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reports\\monthly-revenue\\route.ts": "31", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reports\\outstanding-balances\\route.ts": "32", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reset-db\\route.ts": "33", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\test\\route.ts": "34", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\treatments\\route.ts": "35", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\treatments\\[id]\\route.ts": "36", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\users\\route.ts": "37", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\users\\[id]\\route.ts": "38", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\my-route\\route.ts": "39", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Appointments.ts": "40", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\BillItems.ts": "41", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Bills.ts": "42", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Deposits.ts": "43", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Media.ts": "44", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Patients.ts": "45", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Payments.ts": "46", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Treatments.ts": "47", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Users.ts": "48", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\lib\\auth-sync.ts": "49", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\lib\\clerk-auth-strategy.ts": "50", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\lib\\payload-auth-middleware.ts": "51", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\payload-types.ts": "52", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\payload.config.ts": "53"}, {"size": 400, "mtime": 1752009631000, "results": "54", "hashOfConfig": "55"}, {"size": 1847, "mtime": 1752048389803, "results": "56", "hashOfConfig": "55"}, {"size": 5898, "mtime": 1752016400380, "results": "57", "hashOfConfig": "58"}, {"size": 731, "mtime": 1752009631000, "results": "59", "hashOfConfig": "55"}, {"size": 715, "mtime": 1752009631000, "results": "60", "hashOfConfig": "55"}, {"size": 315, "mtime": 1752009631000, "results": "61", "hashOfConfig": "55"}, {"size": 305, "mtime": 1752009631000, "results": "62", "hashOfConfig": "55"}, {"size": 550, "mtime": 1752009631000, "results": "63", "hashOfConfig": "55"}, {"size": 810, "mtime": 1752009631000, "results": "64", "hashOfConfig": "55"}, {"size": 2212, "mtime": 1752032650690, "results": "65", "hashOfConfig": "55"}, {"size": 3171, "mtime": 1752037068700, "results": "66", "hashOfConfig": "55"}, {"size": 1346, "mtime": 1752018809145, "results": "67", "hashOfConfig": "55"}, {"size": 3305, "mtime": 1752048156005, "results": "68", "hashOfConfig": "55"}, {"size": 3783, "mtime": 1752042973170, "results": "69", "hashOfConfig": "55"}, {"size": 4548, "mtime": 1752049047117, "results": "70", "hashOfConfig": "55"}, {"size": 3707, "mtime": 1752048176586, "results": "71", "hashOfConfig": "55"}, {"size": 3921, "mtime": 1752042932094, "results": "72", "hashOfConfig": "55"}, {"size": 777, "mtime": 1752018791568, "results": "73", "hashOfConfig": "55"}, {"size": 4765, "mtime": 1752088274625, "results": "74", "hashOfConfig": "55"}, {"size": 5064, "mtime": 1752092473781, "results": "75", "hashOfConfig": "55"}, {"size": 3266, "mtime": 1752048189770, "results": "76", "hashOfConfig": "55"}, {"size": 3767, "mtime": 1752043056882, "results": "77", "hashOfConfig": "55"}, {"size": 2308, "mtime": 1752049995084, "results": "78", "hashOfConfig": "55"}, {"size": 2459, "mtime": 1752032674926, "results": "79", "hashOfConfig": "55"}, {"size": 3000, "mtime": 1752037001759, "results": "80", "hashOfConfig": "55"}, {"size": 3659, "mtime": 1752048214156, "results": "81", "hashOfConfig": "55"}, {"size": 5249, "mtime": 1752092952466, "results": "82", "hashOfConfig": "55"}, {"size": 3559, "mtime": 1752043015028, "results": "83", "hashOfConfig": "55"}, {"size": 3350, "mtime": 1752093175446, "results": "84", "hashOfConfig": "55"}, {"size": 5716, "mtime": 1752094372092, "results": "85", "hashOfConfig": "55"}, {"size": 3974, "mtime": 1752093206347, "results": "86", "hashOfConfig": "55"}, {"size": 4250, "mtime": 1752093216697, "results": "87", "hashOfConfig": "55"}, {"size": 1029, "mtime": 1752018919310, "results": "88", "hashOfConfig": "55"}, {"size": 231, "mtime": 1752018622704, "results": "89", "hashOfConfig": "55"}, {"size": 2022, "mtime": 1752032698666, "results": "90", "hashOfConfig": "55"}, {"size": 3036, "mtime": 1752037033875, "results": "91", "hashOfConfig": "55"}, {"size": 1952, "mtime": 1752032614878, "results": "92", "hashOfConfig": "55"}, {"size": 2946, "mtime": 1752036952764, "results": "93", "hashOfConfig": "55"}, {"size": 289, "mtime": 1752048092114, "results": "94", "hashOfConfig": "55"}, {"size": 6433, "mtime": 1752042577243, "results": "95", "hashOfConfig": "55"}, {"size": 4016, "mtime": 1752044074913, "results": "96", "hashOfConfig": "55"}, {"size": 7896, "mtime": 1752085597603, "results": "97", "hashOfConfig": "55"}, {"size": 7285, "mtime": 1752083936008, "results": "98", "hashOfConfig": "55"}, {"size": 255, "mtime": 1752009631000, "results": "99", "hashOfConfig": "55"}, {"size": 6672, "mtime": 1752042473052, "results": "100", "hashOfConfig": "55"}, {"size": 9283, "mtime": 1752094498002, "results": "101", "hashOfConfig": "55"}, {"size": 1401, "mtime": 1752025370327, "results": "102", "hashOfConfig": "55"}, {"size": 2394, "mtime": 1752025408079, "results": "103", "hashOfConfig": "55"}, {"size": 2856, "mtime": 1752044417674, "results": "104", "hashOfConfig": "55"}, {"size": 951, "mtime": 1752044472492, "results": "105", "hashOfConfig": "55"}, {"size": 2700, "mtime": 1752050933716, "results": "106", "hashOfConfig": "55"}, {"size": 18702, "mtime": 1752085476747, "results": "107", "hashOfConfig": "55"}, {"size": 1461, "mtime": 1752042863174, "results": "108", "hashOfConfig": "55"}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1e1u0sl", {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "pn1qvw", {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 35, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(frontend)\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(frontend)\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\admin\\importMap.js", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\admin\\[[...segments]]\\not-found.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\admin\\[[...segments]]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\api\\graphql\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\api\\graphql-playground\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\api\\[...slug]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\appointments\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\appointments\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\appointments-direct\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bill-items\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bill-items\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bills\\generate-from-appointment\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bills\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bills\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\db-test\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\deposits\\apply-to-bill\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\deposits\\refund\\route.ts", ["268", "269", "270", "271", "272", "273", "274", "275", "276"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\deposits\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\deposits\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\patients\\convert-to-patient\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\patients\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\patients\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\payments\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\payments\\[id]\\receipt\\route.ts", ["277", "278", "279", "280", "281", "282", "283", "284", "285", "286", "287", "288", "289", "290", "291", "292", "293", "294", "295", "296", "297", "298", "299", "300"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\payments\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reports\\daily-revenue\\route.ts", ["301", "302", "303", "304", "305", "306", "307", "308"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reports\\financial\\route.ts", ["309", "310", "311", "312", "313", "314", "315", "316", "317", "318", "319", "320", "321", "322", "323", "324", "325", "326", "327", "328", "329", "330", "331", "332", "333", "334", "335", "336", "337", "338", "339", "340", "341", "342", "343"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reports\\monthly-revenue\\route.ts", ["344", "345", "346", "347", "348", "349", "350", "351"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reports\\outstanding-balances\\route.ts", ["352", "353", "354", "355", "356"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reset-db\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\test\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\treatments\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\treatments\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\users\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\users\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\my-route\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Appointments.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\BillItems.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Bills.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Deposits.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Media.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Patients.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Payments.ts", ["357"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Treatments.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Users.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\lib\\auth-sync.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\lib\\clerk-auth-strategy.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\lib\\payload-auth-middleware.ts", ["358", "359", "360", "361"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\payload-types.ts", [], ["362", "363", "364", "365", "366"], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\payload.config.ts", [], [], {"ruleId": "367", "severity": 1, "message": "368", "line": 52, "column": 10, "nodeType": "369", "messageId": "370", "endLine": 52, "endColumn": 13, "suggestions": "371"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 62, "column": 44, "nodeType": "369", "messageId": "370", "endLine": 62, "endColumn": 47, "suggestions": "372"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 62, "column": 81, "nodeType": "369", "messageId": "370", "endLine": 62, "endColumn": 84, "suggestions": "373"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 62, "column": 108, "nodeType": "369", "messageId": "370", "endLine": 62, "endColumn": 111, "suggestions": "374"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 140, "column": 41, "nodeType": "369", "messageId": "370", "endLine": 140, "endColumn": 44, "suggestions": "375"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 141, "column": 40, "nodeType": "369", "messageId": "370", "endLine": 141, "endColumn": 43, "suggestions": "376"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 142, "column": 37, "nodeType": "369", "messageId": "370", "endLine": 142, "endColumn": 40, "suggestions": "377"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 150, "column": 31, "nodeType": "369", "messageId": "370", "endLine": 150, "endColumn": 34, "suggestions": "378"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 151, "column": 42, "nodeType": "369", "messageId": "370", "endLine": 151, "endColumn": 45, "suggestions": "379"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 43, "column": 21, "nodeType": "369", "messageId": "370", "endLine": 43, "endColumn": 24, "suggestions": "380"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 52, "column": 34, "nodeType": "369", "messageId": "370", "endLine": 52, "endColumn": 37, "suggestions": "381"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 53, "column": 34, "nodeType": "369", "messageId": "370", "endLine": 53, "endColumn": 37, "suggestions": "382"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 54, "column": 32, "nodeType": "369", "messageId": "370", "endLine": 54, "endColumn": 35, "suggestions": "383"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 55, "column": 27, "nodeType": "369", "messageId": "370", "endLine": 55, "endColumn": 30, "suggestions": "384"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 56, "column": 34, "nodeType": "369", "messageId": "370", "endLine": 56, "endColumn": 37, "suggestions": "385"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 60, "column": 33, "nodeType": "369", "messageId": "370", "endLine": 60, "endColumn": 36, "suggestions": "386"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 61, "column": 34, "nodeType": "369", "messageId": "370", "endLine": 61, "endColumn": 37, "suggestions": "387"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 62, "column": 34, "nodeType": "369", "messageId": "370", "endLine": 62, "endColumn": 37, "suggestions": "388"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 63, "column": 38, "nodeType": "369", "messageId": "370", "endLine": 63, "endColumn": 41, "suggestions": "389"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 68, "column": 31, "nodeType": "369", "messageId": "370", "endLine": 68, "endColumn": 34, "suggestions": "390"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 69, "column": 28, "nodeType": "369", "messageId": "370", "endLine": 69, "endColumn": 31, "suggestions": "391"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 70, "column": 28, "nodeType": "369", "messageId": "370", "endLine": 70, "endColumn": 31, "suggestions": "392"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 75, "column": 32, "nodeType": "369", "messageId": "370", "endLine": 75, "endColumn": 35, "suggestions": "393"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 76, "column": 31, "nodeType": "369", "messageId": "370", "endLine": 76, "endColumn": 34, "suggestions": "394"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 77, "column": 28, "nodeType": "369", "messageId": "370", "endLine": 77, "endColumn": 31, "suggestions": "395"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 88, "column": 26, "nodeType": "369", "messageId": "370", "endLine": 88, "endColumn": 29, "suggestions": "396"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 89, "column": 34, "nodeType": "369", "messageId": "370", "endLine": 89, "endColumn": 37, "suggestions": "397"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 92, "column": 35, "nodeType": "369", "messageId": "370", "endLine": 92, "endColumn": 38, "suggestions": "398"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 93, "column": 36, "nodeType": "369", "messageId": "370", "endLine": 93, "endColumn": 39, "suggestions": "399"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 94, "column": 34, "nodeType": "369", "messageId": "370", "endLine": 94, "endColumn": 37, "suggestions": "400"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 100, "column": 41, "nodeType": "369", "messageId": "370", "endLine": 100, "endColumn": 44, "suggestions": "401"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 101, "column": 40, "nodeType": "369", "messageId": "370", "endLine": 101, "endColumn": 43, "suggestions": "402"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 102, "column": 37, "nodeType": "369", "messageId": "370", "endLine": 102, "endColumn": 40, "suggestions": "403"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 75, "column": 39, "nodeType": "369", "messageId": "370", "endLine": 75, "endColumn": 42, "suggestions": "404"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 75, "column": 79, "nodeType": "369", "messageId": "370", "endLine": 75, "endColumn": 82, "suggestions": "405"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 76, "column": 39, "nodeType": "369", "messageId": "370", "endLine": 76, "endColumn": 42, "suggestions": "406"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 81, "column": 18, "nodeType": "369", "messageId": "370", "endLine": 81, "endColumn": 21, "suggestions": "407"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 81, "column": 46, "nodeType": "369", "messageId": "370", "endLine": 81, "endColumn": 49, "suggestions": "408"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 97, "column": 41, "nodeType": "369", "messageId": "370", "endLine": 97, "endColumn": 44, "suggestions": "409"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 98, "column": 40, "nodeType": "369", "messageId": "370", "endLine": 98, "endColumn": 43, "suggestions": "410"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 99, "column": 37, "nodeType": "369", "messageId": "370", "endLine": 99, "endColumn": 40, "suggestions": "411"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 83, "column": 34, "nodeType": "369", "messageId": "370", "endLine": 83, "endColumn": 37, "suggestions": "412"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 83, "column": 71, "nodeType": "369", "messageId": "370", "endLine": 83, "endColumn": 74, "suggestions": "413"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 84, "column": 33, "nodeType": "369", "messageId": "370", "endLine": 84, "endColumn": 36, "suggestions": "414"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 84, "column": 70, "nodeType": "369", "messageId": "370", "endLine": 84, "endColumn": 73, "suggestions": "415"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 85, "column": 38, "nodeType": "369", "messageId": "370", "endLine": 85, "endColumn": 41, "suggestions": "416"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 85, "column": 75, "nodeType": "369", "messageId": "370", "endLine": 85, "endColumn": 78, "suggestions": "417"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 87, "column": 40, "nodeType": "369", "messageId": "370", "endLine": 87, "endColumn": 43, "suggestions": "418"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 87, "column": 80, "nodeType": "369", "messageId": "370", "endLine": 87, "endColumn": 83, "suggestions": "419"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 89, "column": 40, "nodeType": "369", "messageId": "370", "endLine": 89, "endColumn": 43, "suggestions": "420"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 89, "column": 80, "nodeType": "369", "messageId": "370", "endLine": 89, "endColumn": 83, "suggestions": "421"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 90, "column": 39, "nodeType": "369", "messageId": "370", "endLine": 90, "endColumn": 42, "suggestions": "422"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 90, "column": 79, "nodeType": "369", "messageId": "370", "endLine": 90, "endColumn": 82, "suggestions": "423"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 91, "column": 44, "nodeType": "369", "messageId": "370", "endLine": 91, "endColumn": 47, "suggestions": "424"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 91, "column": 84, "nodeType": "369", "messageId": "370", "endLine": 91, "endColumn": 87, "suggestions": "425"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 94, "column": 49, "nodeType": "369", "messageId": "370", "endLine": 94, "endColumn": 52, "suggestions": "426"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 94, "column": 72, "nodeType": "369", "messageId": "370", "endLine": 94, "endColumn": 75, "suggestions": "427"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 94, "column": 86, "nodeType": "369", "messageId": "370", "endLine": 94, "endColumn": 89, "suggestions": "428"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 101, "column": 43, "nodeType": "369", "messageId": "370", "endLine": 101, "endColumn": 46, "suggestions": "429"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 101, "column": 66, "nodeType": "369", "messageId": "370", "endLine": 101, "endColumn": 69, "suggestions": "430"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 101, "column": 77, "nodeType": "369", "messageId": "370", "endLine": 101, "endColumn": 80, "suggestions": "431"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 108, "column": 41, "nodeType": "369", "messageId": "370", "endLine": 108, "endColumn": 44, "suggestions": "432"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 108, "column": 64, "nodeType": "369", "messageId": "370", "endLine": 108, "endColumn": 67, "suggestions": "433"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 108, "column": 75, "nodeType": "369", "messageId": "370", "endLine": 108, "endColumn": 78, "suggestions": "434"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 121, "column": 28, "nodeType": "369", "messageId": "370", "endLine": 121, "endColumn": 31, "suggestions": "435"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 128, "column": 31, "nodeType": "369", "messageId": "370", "endLine": 128, "endColumn": 34, "suggestions": "436"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 130, "column": 40, "nodeType": "369", "messageId": "370", "endLine": 130, "endColumn": 43, "suggestions": "437"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 130, "column": 91, "nodeType": "369", "messageId": "370", "endLine": 130, "endColumn": 94, "suggestions": "438"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 133, "column": 31, "nodeType": "369", "messageId": "370", "endLine": 133, "endColumn": 34, "suggestions": "439"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 147, "column": 41, "nodeType": "369", "messageId": "370", "endLine": 147, "endColumn": 44, "suggestions": "440"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 148, "column": 40, "nodeType": "369", "messageId": "370", "endLine": 148, "endColumn": 43, "suggestions": "441"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 149, "column": 37, "nodeType": "369", "messageId": "370", "endLine": 149, "endColumn": 40, "suggestions": "442"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 155, "column": 22, "nodeType": "369", "messageId": "370", "endLine": 155, "endColumn": 25, "suggestions": "443"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 156, "column": 26, "nodeType": "369", "messageId": "370", "endLine": 156, "endColumn": 29, "suggestions": "444"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 157, "column": 32, "nodeType": "369", "messageId": "370", "endLine": 157, "endColumn": 35, "suggestions": "445"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 158, "column": 32, "nodeType": "369", "messageId": "370", "endLine": 158, "endColumn": 35, "suggestions": "446"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 74, "column": 39, "nodeType": "369", "messageId": "370", "endLine": 74, "endColumn": 42, "suggestions": "447"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 74, "column": 79, "nodeType": "369", "messageId": "370", "endLine": 74, "endColumn": 82, "suggestions": "448"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 88, "column": 18, "nodeType": "369", "messageId": "370", "endLine": 88, "endColumn": 21, "suggestions": "449"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 88, "column": 46, "nodeType": "369", "messageId": "370", "endLine": 88, "endColumn": 49, "suggestions": "450"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 108, "column": 34, "nodeType": "369", "messageId": "370", "endLine": 108, "endColumn": 37, "suggestions": "451"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 113, "column": 41, "nodeType": "369", "messageId": "370", "endLine": 113, "endColumn": 44, "suggestions": "452"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 114, "column": 40, "nodeType": "369", "messageId": "370", "endLine": 114, "endColumn": 43, "suggestions": "453"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 115, "column": 37, "nodeType": "369", "messageId": "370", "endLine": 115, "endColumn": 40, "suggestions": "454"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 63, "column": 15, "nodeType": "369", "messageId": "370", "endLine": 63, "endColumn": 18, "suggestions": "455"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 63, "column": 40, "nodeType": "369", "messageId": "370", "endLine": 63, "endColumn": 43, "suggestions": "456"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 118, "column": 41, "nodeType": "369", "messageId": "370", "endLine": 118, "endColumn": 44, "suggestions": "457"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 119, "column": 40, "nodeType": "369", "messageId": "370", "endLine": 119, "endColumn": 43, "suggestions": "458"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 120, "column": 37, "nodeType": "369", "messageId": "370", "endLine": 120, "endColumn": 40, "suggestions": "459"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 91, "column": 27, "nodeType": "369", "messageId": "370", "endLine": 91, "endColumn": 30, "suggestions": "460"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 78, "column": 12, "nodeType": "369", "messageId": "370", "endLine": 78, "endColumn": 15, "suggestions": "461"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 85, "column": 12, "nodeType": "369", "messageId": "370", "endLine": 85, "endColumn": 15, "suggestions": "462"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 92, "column": 12, "nodeType": "369", "messageId": "370", "endLine": 92, "endColumn": 15, "suggestions": "463"}, {"ruleId": "367", "severity": 1, "message": "368", "line": 99, "column": 12, "nodeType": "369", "messageId": "370", "endLine": 99, "endColumn": 15, "suggestions": "464"}, {"ruleId": "465", "severity": 1, "message": "466", "line": 68, "column": 11, "nodeType": "467", "messageId": "468", "endLine": 68, "endColumn": 13, "suggestions": "469", "suppressions": "470"}, {"ruleId": "465", "severity": 1, "message": "466", "line": 83, "column": 21, "nodeType": "467", "messageId": "468", "endLine": 83, "endColumn": 23, "suggestions": "471", "suppressions": "472"}, {"ruleId": "465", "severity": 1, "message": "466", "line": 101, "column": 12, "nodeType": "467", "messageId": "468", "endLine": 101, "endColumn": 14, "suggestions": "473", "suppressions": "474"}, {"ruleId": "465", "severity": 1, "message": "466", "line": 102, "column": 18, "nodeType": "467", "messageId": "468", "endLine": 102, "endColumn": 20, "suggestions": "475", "suppressions": "476"}, {"ruleId": "465", "severity": 1, "message": "477", "line": 830, "column": 20, "nodeType": "478", "messageId": "479", "endLine": 830, "endColumn": 34, "suggestions": "480", "suppressions": "481"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["482", "483"], ["484", "485"], ["486", "487"], ["488", "489"], ["490", "491"], ["492", "493"], ["494", "495"], ["496", "497"], ["498", "499"], ["500", "501"], ["502", "503"], ["504", "505"], ["506", "507"], ["508", "509"], ["510", "511"], ["512", "513"], ["514", "515"], ["516", "517"], ["518", "519"], ["520", "521"], ["522", "523"], ["524", "525"], ["526", "527"], ["528", "529"], ["530", "531"], ["532", "533"], ["534", "535"], ["536", "537"], ["538", "539"], ["540", "541"], ["542", "543"], ["544", "545"], ["546", "547"], ["548", "549"], ["550", "551"], ["552", "553"], ["554", "555"], ["556", "557"], ["558", "559"], ["560", "561"], ["562", "563"], ["564", "565"], ["566", "567"], ["568", "569"], ["570", "571"], ["572", "573"], ["574", "575"], ["576", "577"], ["578", "579"], ["580", "581"], ["582", "583"], ["584", "585"], ["586", "587"], ["588", "589"], ["590", "591"], ["592", "593"], ["594", "595"], ["596", "597"], ["598", "599"], ["600", "601"], ["602", "603"], ["604", "605"], ["606", "607"], ["608", "609"], ["610", "611"], ["612", "613"], ["614", "615"], ["616", "617"], ["618", "619"], ["620", "621"], ["622", "623"], ["624", "625"], ["626", "627"], ["628", "629"], ["630", "631"], ["632", "633"], ["634", "635"], ["636", "637"], ["638", "639"], ["640", "641"], ["642", "643"], ["644", "645"], ["646", "647"], ["648", "649"], ["650", "651"], ["652", "653"], ["654", "655"], ["656", "657"], ["658", "659"], ["660", "661"], ["662", "663"], ["664", "665"], ["666", "667"], ["668", "669"], "@typescript-eslint/no-empty-object-type", "The `{}` (\"empty object\") type allows any non-nullish value, including literals like `0` and `\"\"`.\n- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.\n- If you want a type meaning \"any object\", you probably want `object` instead.\n- If you want a type meaning \"any value\", you probably want `unknown` instead.", "TSTypeLiteral", "noEmptyObject", ["670", "671"], ["672"], ["673", "674"], ["675"], ["676", "677"], ["678"], ["679", "680"], ["681"], "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["682"], ["683"], {"messageId": "684", "fix": "685", "desc": "686"}, {"messageId": "687", "fix": "688", "desc": "689"}, {"messageId": "684", "fix": "690", "desc": "686"}, {"messageId": "687", "fix": "691", "desc": "689"}, {"messageId": "684", "fix": "692", "desc": "686"}, {"messageId": "687", "fix": "693", "desc": "689"}, {"messageId": "684", "fix": "694", "desc": "686"}, {"messageId": "687", "fix": "695", "desc": "689"}, {"messageId": "684", "fix": "696", "desc": "686"}, {"messageId": "687", "fix": "697", "desc": "689"}, {"messageId": "684", "fix": "698", "desc": "686"}, {"messageId": "687", "fix": "699", "desc": "689"}, {"messageId": "684", "fix": "700", "desc": "686"}, {"messageId": "687", "fix": "701", "desc": "689"}, {"messageId": "684", "fix": "702", "desc": "686"}, {"messageId": "687", "fix": "703", "desc": "689"}, {"messageId": "684", "fix": "704", "desc": "686"}, {"messageId": "687", "fix": "705", "desc": "689"}, {"messageId": "684", "fix": "706", "desc": "686"}, {"messageId": "687", "fix": "707", "desc": "689"}, {"messageId": "684", "fix": "708", "desc": "686"}, {"messageId": "687", "fix": "709", "desc": "689"}, {"messageId": "684", "fix": "710", "desc": "686"}, {"messageId": "687", "fix": "711", "desc": "689"}, {"messageId": "684", "fix": "712", "desc": "686"}, {"messageId": "687", "fix": "713", "desc": "689"}, {"messageId": "684", "fix": "714", "desc": "686"}, {"messageId": "687", "fix": "715", "desc": "689"}, {"messageId": "684", "fix": "716", "desc": "686"}, {"messageId": "687", "fix": "717", "desc": "689"}, {"messageId": "684", "fix": "718", "desc": "686"}, {"messageId": "687", "fix": "719", "desc": "689"}, {"messageId": "684", "fix": "720", "desc": "686"}, {"messageId": "687", "fix": "721", "desc": "689"}, {"messageId": "684", "fix": "722", "desc": "686"}, {"messageId": "687", "fix": "723", "desc": "689"}, {"messageId": "684", "fix": "724", "desc": "686"}, {"messageId": "687", "fix": "725", "desc": "689"}, {"messageId": "684", "fix": "726", "desc": "686"}, {"messageId": "687", "fix": "727", "desc": "689"}, {"messageId": "684", "fix": "728", "desc": "686"}, {"messageId": "687", "fix": "729", "desc": "689"}, {"messageId": "684", "fix": "730", "desc": "686"}, {"messageId": "687", "fix": "731", "desc": "689"}, {"messageId": "684", "fix": "732", "desc": "686"}, {"messageId": "687", "fix": "733", "desc": "689"}, {"messageId": "684", "fix": "734", "desc": "686"}, {"messageId": "687", "fix": "735", "desc": "689"}, {"messageId": "684", "fix": "736", "desc": "686"}, {"messageId": "687", "fix": "737", "desc": "689"}, {"messageId": "684", "fix": "738", "desc": "686"}, {"messageId": "687", "fix": "739", "desc": "689"}, {"messageId": "684", "fix": "740", "desc": "686"}, {"messageId": "687", "fix": "741", "desc": "689"}, {"messageId": "684", "fix": "742", "desc": "686"}, {"messageId": "687", "fix": "743", "desc": "689"}, {"messageId": "684", "fix": "744", "desc": "686"}, {"messageId": "687", "fix": "745", "desc": "689"}, {"messageId": "684", "fix": "746", "desc": "686"}, {"messageId": "687", "fix": "747", "desc": "689"}, {"messageId": "684", "fix": "748", "desc": "686"}, {"messageId": "687", "fix": "749", "desc": "689"}, {"messageId": "684", "fix": "750", "desc": "686"}, {"messageId": "687", "fix": "751", "desc": "689"}, {"messageId": "684", "fix": "752", "desc": "686"}, {"messageId": "687", "fix": "753", "desc": "689"}, {"messageId": "684", "fix": "754", "desc": "686"}, {"messageId": "687", "fix": "755", "desc": "689"}, {"messageId": "684", "fix": "756", "desc": "686"}, {"messageId": "687", "fix": "757", "desc": "689"}, {"messageId": "684", "fix": "758", "desc": "686"}, {"messageId": "687", "fix": "759", "desc": "689"}, {"messageId": "684", "fix": "760", "desc": "686"}, {"messageId": "687", "fix": "761", "desc": "689"}, {"messageId": "684", "fix": "762", "desc": "686"}, {"messageId": "687", "fix": "763", "desc": "689"}, {"messageId": "684", "fix": "764", "desc": "686"}, {"messageId": "687", "fix": "765", "desc": "689"}, {"messageId": "684", "fix": "766", "desc": "686"}, {"messageId": "687", "fix": "767", "desc": "689"}, {"messageId": "684", "fix": "768", "desc": "686"}, {"messageId": "687", "fix": "769", "desc": "689"}, {"messageId": "684", "fix": "770", "desc": "686"}, {"messageId": "687", "fix": "771", "desc": "689"}, {"messageId": "684", "fix": "772", "desc": "686"}, {"messageId": "687", "fix": "773", "desc": "689"}, {"messageId": "684", "fix": "774", "desc": "686"}, {"messageId": "687", "fix": "775", "desc": "689"}, {"messageId": "684", "fix": "776", "desc": "686"}, {"messageId": "687", "fix": "777", "desc": "689"}, {"messageId": "684", "fix": "778", "desc": "686"}, {"messageId": "687", "fix": "779", "desc": "689"}, {"messageId": "684", "fix": "780", "desc": "686"}, {"messageId": "687", "fix": "781", "desc": "689"}, {"messageId": "684", "fix": "782", "desc": "686"}, {"messageId": "687", "fix": "783", "desc": "689"}, {"messageId": "684", "fix": "784", "desc": "686"}, {"messageId": "687", "fix": "785", "desc": "689"}, {"messageId": "684", "fix": "786", "desc": "686"}, {"messageId": "687", "fix": "787", "desc": "689"}, {"messageId": "684", "fix": "788", "desc": "686"}, {"messageId": "687", "fix": "789", "desc": "689"}, {"messageId": "684", "fix": "790", "desc": "686"}, {"messageId": "687", "fix": "791", "desc": "689"}, {"messageId": "684", "fix": "792", "desc": "686"}, {"messageId": "687", "fix": "793", "desc": "689"}, {"messageId": "684", "fix": "794", "desc": "686"}, {"messageId": "687", "fix": "795", "desc": "689"}, {"messageId": "684", "fix": "796", "desc": "686"}, {"messageId": "687", "fix": "797", "desc": "689"}, {"messageId": "684", "fix": "798", "desc": "686"}, {"messageId": "687", "fix": "799", "desc": "689"}, {"messageId": "684", "fix": "800", "desc": "686"}, {"messageId": "687", "fix": "801", "desc": "689"}, {"messageId": "684", "fix": "802", "desc": "686"}, {"messageId": "687", "fix": "803", "desc": "689"}, {"messageId": "684", "fix": "804", "desc": "686"}, {"messageId": "687", "fix": "805", "desc": "689"}, {"messageId": "684", "fix": "806", "desc": "686"}, {"messageId": "687", "fix": "807", "desc": "689"}, {"messageId": "684", "fix": "808", "desc": "686"}, {"messageId": "687", "fix": "809", "desc": "689"}, {"messageId": "684", "fix": "810", "desc": "686"}, {"messageId": "687", "fix": "811", "desc": "689"}, {"messageId": "684", "fix": "812", "desc": "686"}, {"messageId": "687", "fix": "813", "desc": "689"}, {"messageId": "684", "fix": "814", "desc": "686"}, {"messageId": "687", "fix": "815", "desc": "689"}, {"messageId": "684", "fix": "816", "desc": "686"}, {"messageId": "687", "fix": "817", "desc": "689"}, {"messageId": "684", "fix": "818", "desc": "686"}, {"messageId": "687", "fix": "819", "desc": "689"}, {"messageId": "684", "fix": "820", "desc": "686"}, {"messageId": "687", "fix": "821", "desc": "689"}, {"messageId": "684", "fix": "822", "desc": "686"}, {"messageId": "687", "fix": "823", "desc": "689"}, {"messageId": "684", "fix": "824", "desc": "686"}, {"messageId": "687", "fix": "825", "desc": "689"}, {"messageId": "684", "fix": "826", "desc": "686"}, {"messageId": "687", "fix": "827", "desc": "689"}, {"messageId": "684", "fix": "828", "desc": "686"}, {"messageId": "687", "fix": "829", "desc": "689"}, {"messageId": "684", "fix": "830", "desc": "686"}, {"messageId": "687", "fix": "831", "desc": "689"}, {"messageId": "684", "fix": "832", "desc": "686"}, {"messageId": "687", "fix": "833", "desc": "689"}, {"messageId": "684", "fix": "834", "desc": "686"}, {"messageId": "687", "fix": "835", "desc": "689"}, {"messageId": "684", "fix": "836", "desc": "686"}, {"messageId": "687", "fix": "837", "desc": "689"}, {"messageId": "684", "fix": "838", "desc": "686"}, {"messageId": "687", "fix": "839", "desc": "689"}, {"messageId": "684", "fix": "840", "desc": "686"}, {"messageId": "687", "fix": "841", "desc": "689"}, {"messageId": "684", "fix": "842", "desc": "686"}, {"messageId": "687", "fix": "843", "desc": "689"}, {"messageId": "684", "fix": "844", "desc": "686"}, {"messageId": "687", "fix": "845", "desc": "689"}, {"messageId": "684", "fix": "846", "desc": "686"}, {"messageId": "687", "fix": "847", "desc": "689"}, {"messageId": "684", "fix": "848", "desc": "686"}, {"messageId": "687", "fix": "849", "desc": "689"}, {"messageId": "684", "fix": "850", "desc": "686"}, {"messageId": "687", "fix": "851", "desc": "689"}, {"messageId": "684", "fix": "852", "desc": "686"}, {"messageId": "687", "fix": "853", "desc": "689"}, {"messageId": "684", "fix": "854", "desc": "686"}, {"messageId": "687", "fix": "855", "desc": "689"}, {"messageId": "684", "fix": "856", "desc": "686"}, {"messageId": "687", "fix": "857", "desc": "689"}, {"messageId": "684", "fix": "858", "desc": "686"}, {"messageId": "687", "fix": "859", "desc": "689"}, {"messageId": "684", "fix": "860", "desc": "686"}, {"messageId": "687", "fix": "861", "desc": "689"}, {"messageId": "684", "fix": "862", "desc": "686"}, {"messageId": "687", "fix": "863", "desc": "689"}, {"messageId": "684", "fix": "864", "desc": "686"}, {"messageId": "687", "fix": "865", "desc": "689"}, {"messageId": "684", "fix": "866", "desc": "686"}, {"messageId": "687", "fix": "867", "desc": "689"}, {"messageId": "684", "fix": "868", "desc": "686"}, {"messageId": "687", "fix": "869", "desc": "689"}, {"messageId": "684", "fix": "870", "desc": "686"}, {"messageId": "687", "fix": "871", "desc": "689"}, {"messageId": "684", "fix": "872", "desc": "686"}, {"messageId": "687", "fix": "873", "desc": "689"}, {"messageId": "684", "fix": "874", "desc": "686"}, {"messageId": "687", "fix": "875", "desc": "689"}, {"messageId": "876", "data": "877", "fix": "878", "desc": "879"}, {"messageId": "876", "data": "880", "fix": "881", "desc": "882"}, {"kind": "883", "justification": "884"}, {"messageId": "876", "data": "885", "fix": "886", "desc": "879"}, {"messageId": "876", "data": "887", "fix": "888", "desc": "882"}, {"kind": "883", "justification": "884"}, {"messageId": "876", "data": "889", "fix": "890", "desc": "879"}, {"messageId": "876", "data": "891", "fix": "892", "desc": "882"}, {"kind": "883", "justification": "884"}, {"messageId": "876", "data": "893", "fix": "894", "desc": "879"}, {"messageId": "876", "data": "895", "fix": "896", "desc": "882"}, {"kind": "883", "justification": "884"}, {"messageId": "897", "fix": "898", "desc": "899"}, {"kind": "883", "justification": "884"}, "suggestUnknown", {"range": "900", "text": "901"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "902", "text": "903"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "904", "text": "901"}, {"range": "905", "text": "903"}, {"range": "906", "text": "901"}, {"range": "907", "text": "903"}, {"range": "908", "text": "901"}, {"range": "909", "text": "903"}, {"range": "910", "text": "901"}, {"range": "911", "text": "903"}, {"range": "912", "text": "901"}, {"range": "913", "text": "903"}, {"range": "914", "text": "901"}, {"range": "915", "text": "903"}, {"range": "916", "text": "901"}, {"range": "917", "text": "903"}, {"range": "918", "text": "901"}, {"range": "919", "text": "903"}, {"range": "920", "text": "901"}, {"range": "921", "text": "903"}, {"range": "922", "text": "901"}, {"range": "923", "text": "903"}, {"range": "924", "text": "901"}, {"range": "925", "text": "903"}, {"range": "926", "text": "901"}, {"range": "927", "text": "903"}, {"range": "928", "text": "901"}, {"range": "929", "text": "903"}, {"range": "930", "text": "901"}, {"range": "931", "text": "903"}, {"range": "932", "text": "901"}, {"range": "933", "text": "903"}, {"range": "934", "text": "901"}, {"range": "935", "text": "903"}, {"range": "936", "text": "901"}, {"range": "937", "text": "903"}, {"range": "938", "text": "901"}, {"range": "939", "text": "903"}, {"range": "940", "text": "901"}, {"range": "941", "text": "903"}, {"range": "942", "text": "901"}, {"range": "943", "text": "903"}, {"range": "944", "text": "901"}, {"range": "945", "text": "903"}, {"range": "946", "text": "901"}, {"range": "947", "text": "903"}, {"range": "948", "text": "901"}, {"range": "949", "text": "903"}, {"range": "950", "text": "901"}, {"range": "951", "text": "903"}, {"range": "952", "text": "901"}, {"range": "953", "text": "903"}, {"range": "954", "text": "901"}, {"range": "955", "text": "903"}, {"range": "956", "text": "901"}, {"range": "957", "text": "903"}, {"range": "958", "text": "901"}, {"range": "959", "text": "903"}, {"range": "960", "text": "901"}, {"range": "961", "text": "903"}, {"range": "962", "text": "901"}, {"range": "963", "text": "903"}, {"range": "964", "text": "901"}, {"range": "965", "text": "903"}, {"range": "966", "text": "901"}, {"range": "967", "text": "903"}, {"range": "968", "text": "901"}, {"range": "969", "text": "903"}, {"range": "970", "text": "901"}, {"range": "971", "text": "903"}, {"range": "972", "text": "901"}, {"range": "973", "text": "903"}, {"range": "974", "text": "901"}, {"range": "975", "text": "903"}, {"range": "976", "text": "901"}, {"range": "977", "text": "903"}, {"range": "978", "text": "901"}, {"range": "979", "text": "903"}, {"range": "980", "text": "901"}, {"range": "981", "text": "903"}, {"range": "982", "text": "901"}, {"range": "983", "text": "903"}, {"range": "984", "text": "901"}, {"range": "985", "text": "903"}, {"range": "986", "text": "901"}, {"range": "987", "text": "903"}, {"range": "988", "text": "901"}, {"range": "989", "text": "903"}, {"range": "990", "text": "901"}, {"range": "991", "text": "903"}, {"range": "992", "text": "901"}, {"range": "993", "text": "903"}, {"range": "994", "text": "901"}, {"range": "995", "text": "903"}, {"range": "996", "text": "901"}, {"range": "997", "text": "903"}, {"range": "998", "text": "901"}, {"range": "999", "text": "903"}, {"range": "1000", "text": "901"}, {"range": "1001", "text": "903"}, {"range": "1002", "text": "901"}, {"range": "1003", "text": "903"}, {"range": "1004", "text": "901"}, {"range": "1005", "text": "903"}, {"range": "1006", "text": "901"}, {"range": "1007", "text": "903"}, {"range": "1008", "text": "901"}, {"range": "1009", "text": "903"}, {"range": "1010", "text": "901"}, {"range": "1011", "text": "903"}, {"range": "1012", "text": "901"}, {"range": "1013", "text": "903"}, {"range": "1014", "text": "901"}, {"range": "1015", "text": "903"}, {"range": "1016", "text": "901"}, {"range": "1017", "text": "903"}, {"range": "1018", "text": "901"}, {"range": "1019", "text": "903"}, {"range": "1020", "text": "901"}, {"range": "1021", "text": "903"}, {"range": "1022", "text": "901"}, {"range": "1023", "text": "903"}, {"range": "1024", "text": "901"}, {"range": "1025", "text": "903"}, {"range": "1026", "text": "901"}, {"range": "1027", "text": "903"}, {"range": "1028", "text": "901"}, {"range": "1029", "text": "903"}, {"range": "1030", "text": "901"}, {"range": "1031", "text": "903"}, {"range": "1032", "text": "901"}, {"range": "1033", "text": "903"}, {"range": "1034", "text": "901"}, {"range": "1035", "text": "903"}, {"range": "1036", "text": "901"}, {"range": "1037", "text": "903"}, {"range": "1038", "text": "901"}, {"range": "1039", "text": "903"}, {"range": "1040", "text": "901"}, {"range": "1041", "text": "903"}, {"range": "1042", "text": "901"}, {"range": "1043", "text": "903"}, {"range": "1044", "text": "901"}, {"range": "1045", "text": "903"}, {"range": "1046", "text": "901"}, {"range": "1047", "text": "903"}, {"range": "1048", "text": "901"}, {"range": "1049", "text": "903"}, {"range": "1050", "text": "901"}, {"range": "1051", "text": "903"}, {"range": "1052", "text": "901"}, {"range": "1053", "text": "903"}, {"range": "1054", "text": "901"}, {"range": "1055", "text": "903"}, {"range": "1056", "text": "901"}, {"range": "1057", "text": "903"}, {"range": "1058", "text": "901"}, {"range": "1059", "text": "903"}, {"range": "1060", "text": "901"}, {"range": "1061", "text": "903"}, {"range": "1062", "text": "901"}, {"range": "1063", "text": "903"}, {"range": "1064", "text": "901"}, {"range": "1065", "text": "903"}, {"range": "1066", "text": "901"}, {"range": "1067", "text": "903"}, {"range": "1068", "text": "901"}, {"range": "1069", "text": "903"}, {"range": "1070", "text": "901"}, {"range": "1071", "text": "903"}, {"range": "1072", "text": "901"}, {"range": "1073", "text": "903"}, {"range": "1074", "text": "901"}, {"range": "1075", "text": "903"}, {"range": "1076", "text": "901"}, {"range": "1077", "text": "903"}, {"range": "1078", "text": "901"}, {"range": "1079", "text": "903"}, {"range": "1080", "text": "901"}, {"range": "1081", "text": "903"}, {"range": "1082", "text": "901"}, {"range": "1083", "text": "903"}, {"range": "1084", "text": "901"}, {"range": "1085", "text": "903"}, {"range": "1086", "text": "901"}, {"range": "1087", "text": "903"}, {"range": "1088", "text": "901"}, {"range": "1089", "text": "903"}, "replaceEmptyObjectType", {"replacement": "1090"}, {"range": "1091", "text": "1090"}, "Replace `{}` with `object`.", {"replacement": "901"}, {"range": "1092", "text": "901"}, "Replace `{}` with `unknown`.", "directive", "", {"replacement": "1090"}, {"range": "1093", "text": "1090"}, {"replacement": "901"}, {"range": "1094", "text": "901"}, {"replacement": "1090"}, {"range": "1095", "text": "1090"}, {"replacement": "901"}, {"range": "1096", "text": "901"}, {"replacement": "1090"}, {"range": "1097", "text": "1090"}, {"replacement": "901"}, {"range": "1098", "text": "901"}, "replaceEmptyInterfaceWithSuper", {"range": "1099", "text": "1100"}, "Replace empty interface with a type alias.", [1476, 1479], "unknown", [1476, 1479], "never", [1687, 1690], [1687, 1690], [1724, 1727], [1724, 1727], [1751, 1754], [1751, 1754], [4289, 4292], [4289, 4292], [4344, 4347], [4344, 4347], [4395, 4398], [4395, 4398], [4578, 4581], [4578, 4581], [4628, 4631], [4628, 4631], [1148, 1151], [1148, 1151], [1421, 1424], [1421, 1424], [1474, 1477], [1474, 1477], [1525, 1528], [1525, 1528], [1569, 1572], [1569, 1572], [1615, 1618], [1615, 1618], [1708, 1711], [1708, 1711], [1764, 1767], [1764, 1767], [1821, 1824], [1821, 1824], [1882, 1885], [1882, 1885], [1996, 1999], [1996, 1999], [2047, 2050], [2047, 2050], [2095, 2098], [2095, 2098], [2204, 2207], [2204, 2207], [2262, 2265], [2262, 2265], [2316, 2319], [2316, 2319], [2576, 2579], [2576, 2579], [2621, 2624], [2621, 2624], [2720, 2723], [2720, 2723], [2779, 2782], [2779, 2782], [2848, 2851], [2848, 2851], [3040, 3043], [3040, 3043], [3095, 3098], [3095, 3098], [3146, 3149], [3146, 3149], [2077, 2080], [2077, 2080], [2117, 2120], [2117, 2120], [2189, 2192], [2189, 2192], [2345, 2348], [2345, 2348], [2373, 2376], [2373, 2376], [2920, 2923], [2920, 2923], [2975, 2978], [2975, 2978], [3026, 3029], [3026, 3029], [2265, 2268], [2265, 2268], [2302, 2305], [2302, 2305], [2370, 2373], [2370, 2373], [2407, 2410], [2407, 2410], [2486, 2489], [2486, 2489], [2523, 2526], [2523, 2526], [2610, 2613], [2610, 2613], [2650, 2653], [2650, 2653], [2724, 2727], [2724, 2727], [2764, 2767], [2764, 2767], [2836, 2839], [2836, 2839], [2876, 2879], [2876, 2879], [2964, 2967], [2964, 2967], [3004, 3007], [3004, 3007], [3135, 3138], [3135, 3138], [3158, 3161], [3158, 3161], [3172, 3175], [3172, 3175], [3385, 3388], [3385, 3388], [3408, 3411], [3408, 3411], [3419, 3422], [3419, 3422], [3605, 3608], [3605, 3608], [3628, 3631], [3628, 3631], [3639, 3642], [3639, 3642], [4002, 4005], [4002, 4005], [4280, 4283], [4280, 4283], [4373, 4376], [4373, 4376], [4424, 4427], [4424, 4427], [4517, 4520], [4517, 4520], [5037, 5040], [5037, 5040], [5092, 5095], [5092, 5095], [5143, 5146], [5143, 5146], [5271, 5274], [5271, 5274], [5313, 5316], [5313, 5316], [5355, 5358], [5355, 5358], [5397, 5400], [5397, 5400], [2147, 2150], [2147, 2150], [2187, 2190], [2187, 2190], [2709, 2712], [2709, 2712], [2737, 2740], [2737, 2740], [3319, 3322], [3319, 3322], [3540, 3543], [3540, 3543], [3595, 3598], [3595, 3598], [3646, 3649], [3646, 3649], [1730, 1733], [1730, 1733], [1755, 1758], [1755, 1758], [3806, 3809], [3806, 3809], [3861, 3864], [3861, 3864], [3912, 3915], [3912, 3915], [3215, 3218], [3215, 3218], [2231, 2234], [2231, 2234], [2358, 2361], [2358, 2361], [2485, 2488], [2485, 2488], [2612, 2615], [2612, 2615], "object", [1506, 1508], [1506, 1508], [1904, 1906], [1904, 1906], [2813, 2815], [2813, 2815], [2834, 2836], [2834, 2836], [17528, 17570], "type GeneratedTypes = Config"]