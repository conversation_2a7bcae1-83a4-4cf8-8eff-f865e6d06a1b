// Jest setup file for billing system tests
// Configures testing environment, mocks, and global utilities

import '@testing-library/jest-dom';
import { TextEncoder, TextDecoder } from 'util';

// Polyfills for Node.js environment
global.TextEncoder = TextEncoder;
global.TextDecoder = TextDecoder;

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    pathname: '/dashboard/billing',
    query: {},
    asPath: '/dashboard/billing',
  }),
}));

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
  }),
  usePathname: () => '/dashboard/billing',
  useSearchParams: () => new URLSearchParams(),
}));

// Mock Clerk authentication
jest.mock('@clerk/nextjs', () => ({
  useAuth: () => ({
    isLoaded: true,
    isSignedIn: true,
    userId: 'test-user-id',
    sessionId: 'test-session-id',
    getToken: jest.fn().mockResolvedValue('test-token'),
  }),
  useUser: () => ({
    isLoaded: true,
    isSignedIn: true,
    user: {
      id: 'test-user-id',
      emailAddresses: [{ emailAddress: '<EMAIL>' }],
      fullName: 'Test User',
    },
  }),
  SignInButton: ({ children }) => <button>{children}</button>,
  SignOutButton: ({ children }) => <button>{children}</button>,
  UserButton: () => <div>User Button</div>,
}));

jest.mock('@clerk/nextjs/server', () => ({
  auth: jest.fn().mockResolvedValue({
    userId: 'test-user-id',
    sessionId: 'test-session-id',
  }),
}));

// Mock React hooks
const mockUseState = jest.fn();
const mockUseEffect = jest.fn();
const mockUseCallback = jest.fn();
const mockUseMemo = jest.fn();

// Mock toast notifications
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    warning: jest.fn(),
    info: jest.fn(),
  },
  Toaster: () => <div>Toaster</div>,
}));

// Mock performance APIs
Object.defineProperty(window, 'performance', {
  value: {
    now: jest.fn(() => Date.now()),
    mark: jest.fn(),
    measure: jest.fn(),
    getEntriesByType: jest.fn(() => []),
    memory: {
      usedJSHeapSize: 50 * 1024 * 1024, // 50MB
      totalJSHeapSize: 100 * 1024 * 1024, // 100MB
      jsHeapSizeLimit: 2 * 1024 * 1024 * 1024, // 2GB
    },
  },
  writable: true,
});

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  observe() {}
  unobserve() {}
  disconnect() {}
};

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  observe() {}
  unobserve() {}
  disconnect() {}
};

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock crypto for security tests
Object.defineProperty(global, 'crypto', {
  value: {
    randomBytes: jest.fn().mockReturnValue(Buffer.from('test-random-bytes')),
    createHash: jest.fn().mockReturnValue({
      update: jest.fn().mockReturnThis(),
      digest: jest.fn().mockReturnValue('test-hash'),
    }),
    createCipher: jest.fn().mockReturnValue({
      setAAD: jest.fn(),
      update: jest.fn().mockReturnValue('encrypted'),
      final: jest.fn().mockReturnValue('final'),
      getAuthTag: jest.fn().mockReturnValue(Buffer.from('auth-tag')),
    }),
    createDecipher: jest.fn().mockReturnValue({
      setAAD: jest.fn(),
      setAuthTag: jest.fn(),
      update: jest.fn().mockReturnValue('decrypted'),
      final: jest.fn().mockReturnValue('final'),
    }),
  },
});

// Mock environment variables
process.env.NEXT_PUBLIC_API_URL = 'http://localhost:8002';
process.env.CLERK_SECRET_KEY = 'test-clerk-secret';
process.env.BILLING_ENCRYPTION_KEY = '0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef';

// Global test utilities
global.testUtils = {
  // Mock bill data
  mockBill: {
    id: 'bill-123',
    billNumber: 'BILL-001',
    patient: {
      id: 'patient-123',
      fullName: 'John Doe',
    },
    billType: 'consultation',
    description: 'Medical consultation',
    items: [
      {
        id: 'item-1',
        itemName: 'Consultation Fee',
        quantity: 1,
        unitPrice: 100.00,
        discountRate: 0,
      },
    ],
    subtotal: 100.00,
    totalAmount: 100.00,
    status: 'pending',
    createdAt: '2024-01-01T00:00:00Z',
    dueDate: '2024-01-31T00:00:00Z',
  },
  
  // Mock payment data
  mockPayment: {
    id: 'payment-123',
    billId: 'bill-123',
    amount: 100.00,
    paymentMethod: 'cash',
    transactionId: 'TXN-001',
    notes: 'Cash payment',
    status: 'completed',
    createdAt: '2024-01-01T00:00:00Z',
  },
  
  // Mock API responses
  mockBillsResponse: {
    docs: [],
    totalDocs: 0,
    page: 1,
    limit: 20,
    totalPages: 1,
    hasNext: false,
    hasPrev: false,
  },
  
  // Test helpers
  createMockFetch: (responses = {}) => {
    return jest.fn().mockImplementation((url, options) => {
      const method = options?.method || 'GET';
      const key = `${method} ${url}`;
      
      if (responses[key]) {
        return Promise.resolve({
          ok: true,
          status: 200,
          json: () => Promise.resolve(responses[key]),
        });
      }
      
      return Promise.resolve({
        ok: true,
        status: 200,
        json: () => Promise.resolve({}),
      });
    });
  },
  
  // Wait for async operations
  waitFor: (ms = 0) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Mock component props
  mockComponentProps: {
    className: 'test-class',
    'data-testid': 'test-component',
  },
};

// Console error suppression for known issues
const originalError = console.error;
console.error = (...args) => {
  // Suppress specific warnings that are expected in tests
  const suppressedMessages = [
    'Warning: ReactDOM.render is deprecated',
    'Warning: componentWillReceiveProps has been renamed',
    'Warning: componentWillMount has been renamed',
  ];
  
  const message = args[0];
  if (typeof message === 'string' && suppressedMessages.some(msg => message.includes(msg))) {
    return;
  }
  
  originalError.apply(console, args);
};

// Cleanup after each test
afterEach(() => {
  jest.clearAllMocks();
  
  // Clear any timers
  jest.clearAllTimers();
  
  // Reset DOM
  document.body.innerHTML = '';
  
  // Clear local storage
  localStorage.clear();
  sessionStorage.clear();
});

// Global test timeout
jest.setTimeout(10000);
