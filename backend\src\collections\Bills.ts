import type { CollectionConfig, Access } from 'payload'

export const Bills: CollectionConfig = {
  slug: 'bills',
  admin: {
    useAsTitle: 'billNumber',
    defaultColumns: ['billNumber', 'patient', 'billType', 'status', 'totalAmount', 'remainingAmount'],
    listSearchableFields: ['billNumber', 'patient.fullName', 'description'],
  },
  access: {
    // Read: Admin and Front-desk see all bills, Doctors see only bills related to their appointments
    read: (({ req: { user } }) => {
      if (!user) return false;
      if (user.role === 'admin' || user.role === 'front-desk') {
        return true; // Can see all bills
      }
      if (user.role === 'doctor') {
        return {
          or: [
            {
              'appointment.practitioner': {
                equals: user.id,
              },
            },
            {
              createdBy: {
                equals: user.id,
              },
            },
          ],
        };
      }
      return false;
    }) as Access,

    // Create: Admin and Front-desk can create bills
    create: ({ req: { user } }) => {
      if (!user) return false;
      return user.role === 'admin' || user.role === 'front-desk';
    },

    // Update: Admin and Front-desk can update bills
    update: ({ req: { user } }) => {
      if (!user) return false;
      return user.role === 'admin' || user.role === 'front-desk';
    },

    // Delete: Only Admin can delete bills
    delete: ({ req: { user } }) => {
      if (!user) return false;
      return user.role === 'admin';
    },
  },
  hooks: {
    beforeChange: [
      ({ data }) => {
        // Auto-generate bill number if not provided
        if (!data.billNumber) {
          const now = new Date();
          const year = now.getFullYear();
          const month = String(now.getMonth() + 1).padStart(2, '0');
          const day = String(now.getDate()).padStart(2, '0');
          const timestamp = now.getTime().toString().slice(-6);
          data.billNumber = `BILL-${year}${month}${day}-${timestamp}`;
        }

        // Validate amounts are not negative
        if (data.subtotal !== undefined && data.subtotal < 0) {
          throw new Error('Subtotal cannot be negative');
        }
        if (data.totalAmount !== undefined && data.totalAmount < 0) {
          throw new Error('Total amount cannot be negative');
        }
        if (data.discountAmount !== undefined && data.discountAmount < 0) {
          throw new Error('Discount amount cannot be negative');
        }
        if (data.taxAmount !== undefined && data.taxAmount < 0) {
          throw new Error('Tax amount cannot be negative');
        }
        if (data.paidAmount !== undefined && data.paidAmount < 0) {
          throw new Error('Paid amount cannot be negative');
        }

        // Calculate remaining amount
        const totalAmount = data.totalAmount || 0;
        const paidAmount = data.paidAmount || 0;
        data.remainingAmount = totalAmount - paidAmount;

        // Validate remaining amount is not negative
        if (data.remainingAmount < 0) {
          throw new Error('Paid amount cannot exceed total amount');
        }

        return data;
      },
    ],
  },
  fields: [
    {
      name: 'billNumber',
      type: 'text',
      required: true,
      unique: true,
      label: '账单编号',
      admin: {
        description: '系统自动生成，格式：BILL-YYYYMMDD-XXXXXX',
        readOnly: true,
      },
    },
    
    // 关联信息
    {
      name: 'patient',
      type: 'relationship',
      relationTo: 'patients',
      required: true,
      hasMany: false,
      label: '患者',
    },
    {
      name: 'appointment',
      type: 'relationship',
      relationTo: 'appointments',
      hasMany: false,
      label: '关联预约',
      admin: {
        description: '如果账单来源于预约，请选择对应预约',
      },
    },
    {
      name: 'treatment',
      type: 'relationship',
      relationTo: 'treatments',
      hasMany: false,
      label: '关联治疗',
      admin: {
        description: '如果是治疗账单，请选择对应治疗项目',
      },
    },
    
    // 账单基本信息
    {
      name: 'billType',
      type: 'select',
      required: true,
      options: [
        {
          label: '治疗账单',
          value: 'treatment',
        },
        {
          label: '咨询账单',
          value: 'consultation',
        },
        {
          label: '押金账单',
          value: 'deposit',
        },
        {
          label: '补充账单',
          value: 'additional',
        },
      ],
      defaultValue: 'treatment',
      label: '账单类型',
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      options: [
        {
          label: '草稿',
          value: 'draft',
        },
        {
          label: '已发送',
          value: 'sent',
        },
        {
          label: '已确认',
          value: 'confirmed',
        },
        {
          label: '已支付',
          value: 'paid',
        },
        {
          label: '已取消',
          value: 'cancelled',
        },
      ],
      defaultValue: 'draft',
      label: '账单状态',
    },
    
    // 金额信息
    {
      name: 'subtotal',
      type: 'number',
      required: true,
      label: '小计金额',
      min: 0,
      admin: {
        description: '税前金额',
      },
    },
    {
      name: 'discountAmount',
      type: 'number',
      defaultValue: 0,
      label: '折扣金额',
      min: 0,
      admin: {
        description: '折扣金额',
      },
    },
    {
      name: 'taxAmount',
      type: 'number',
      defaultValue: 0,
      label: '税费',
      min: 0,
      admin: {
        description: '税费金额',
      },
    },
    {
      name: 'totalAmount',
      type: 'number',
      required: true,
      label: '总金额',
      min: 0,
      admin: {
        description: '最终应付金额 = 小计 + 税费 - 折扣',
      },
    },
    {
      name: 'paidAmount',
      type: 'number',
      defaultValue: 0,
      label: '已支付金额',
      min: 0,
      admin: {
        description: '已支付的金额',
      },
    },
    {
      name: 'remainingAmount',
      type: 'number',
      label: '剩余金额',
      admin: {
        description: '剩余未支付金额（自动计算）',
        readOnly: true,
      },
    },
    
    // 时间信息
    {
      name: 'issueDate',
      type: 'date',
      required: true,
      label: '开票日期',
      defaultValue: () => new Date().toISOString(),
      admin: {
        date: {
          pickerAppearance: 'dayOnly',
        },
      },
    },
    {
      name: 'dueDate',
      type: 'date',
      required: true,
      label: '到期日期',
      admin: {
        date: {
          pickerAppearance: 'dayOnly',
        },
        description: '账单到期日期',
      },
    },
    {
      name: 'paidDate',
      type: 'date',
      label: '支付完成日期',
      admin: {
        date: {
          pickerAppearance: 'dayAndTime',
        },
        description: '账单完全支付的日期',
        condition: (data) => data.status === 'paid',
      },
    },
    
    // 详细信息
    {
      name: 'description',
      type: 'text',
      required: true,
      label: '账单描述',
      admin: {
        description: '账单的简要描述',
      },
    },
    {
      name: 'notes',
      type: 'textarea',
      label: '备注',
      admin: {
        description: '账单相关的备注信息',
      },
    },
    
    // 创建人员
    {
      name: 'createdBy',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      hasMany: false,
      label: '创建人员',
      admin: {
        description: '创建此账单的工作人员',
      },
    },
  ],
}
