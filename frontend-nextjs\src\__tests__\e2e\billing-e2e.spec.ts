// End-to-end tests for billing system
// Tests complete user workflows from frontend to backend

import { test, expect, Page } from '@playwright/test';

// Test configuration
const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';
const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:8002';

// Test data
const TEST_USER = {
  email: '<EMAIL>',
  password: 'testpassword123',
};

const TEST_PATIENT = {
  fullName: '<PERSON>',
  phone: '13800138000',
  email: '<EMAIL>',
};

const TEST_BILL = {
  billType: 'consultation',
  description: 'Medical consultation for patient',
  items: [
    {
      itemName: 'Consultation Fee',
      quantity: 1,
      unitPrice: 200.00,
      discountRate: 0,
    },
    {
      itemName: 'Prescription Fee',
      quantity: 1,
      unitPrice: 50.00,
      discountRate: 10,
    },
  ],
};

const TEST_PAYMENT = {
  amount: 245.00, // 200 + (50 - 5) = 245
  paymentMethod: 'cash',
  transactionId: 'CASH-001',
  notes: 'Cash payment for consultation',
};

// Helper functions
async function loginUser(page: Page) {
  await page.goto(`${BASE_URL}/auth/sign-in`);
  
  // Wait for Clerk to load
  await page.waitForSelector('[data-clerk-element="sign-in"]', { timeout: 10000 });
  
  // Fill in email
  await page.fill('input[name="identifier"]', TEST_USER.email);
  await page.click('button[type="submit"]');
  
  // Handle verification code (in development mode)
  await page.waitForSelector('input[name="code"]', { timeout: 5000 });
  await page.fill('input[name="code"]', '424242'); // Development verification code
  
  // Wait for redirect to dashboard
  await page.waitForURL(`${BASE_URL}/dashboard`);
}

async function navigateToBilling(page: Page) {
  await page.click('a[href="/dashboard/billing"]');
  await page.waitForURL(`${BASE_URL}/dashboard/billing`);
  await page.waitForSelector('h2:has-text("财务管理")');
}

async function createTestPatient(page: Page): Promise<string> {
  // Navigate to patients page
  await page.click('a[href="/dashboard/patients"]');
  await page.waitForURL(`${BASE_URL}/dashboard/patients`);
  
  // Create new patient
  await page.click('button:has-text("新建患者")');
  await page.waitForSelector('[role="dialog"]');
  
  // Fill patient form
  await page.fill('input[name="fullName"]', TEST_PATIENT.fullName);
  await page.fill('input[name="phone"]', TEST_PATIENT.phone);
  await page.fill('input[name="email"]', TEST_PATIENT.email);
  
  // Submit form
  await page.click('button[type="submit"]');
  
  // Wait for success message
  await page.waitForSelector('.toast:has-text("患者创建成功")');
  
  // Get patient ID from the created patient row
  const patientRow = page.locator(`tr:has-text("${TEST_PATIENT.fullName}")`);
  const patientId = await patientRow.getAttribute('data-patient-id');
  
  return patientId || 'test-patient-id';
}

test.describe('Billing System E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Set up test environment
    await page.goto(BASE_URL);
  });

  test.describe('Authentication and Navigation', () => {
    test('should login and navigate to billing page', async ({ page }) => {
      await loginUser(page);
      await navigateToBilling(page);
      
      // Verify billing page elements
      await expect(page.locator('h2:has-text("财务管理")')).toBeVisible();
      await expect(page.locator('tab:has-text("账单管理")')).toBeVisible();
      await expect(page.locator('tab:has-text("预约生成")')).toBeVisible();
      await expect(page.locator('tab:has-text("收据管理")')).toBeVisible();
      await expect(page.locator('tab:has-text("财务报表")')).toBeVisible();
    });

    test('should redirect unauthenticated users to sign-in', async ({ page }) => {
      await page.goto(`${BASE_URL}/dashboard/billing`);
      await page.waitForURL(/.*sign-in.*/);
      
      await expect(page.locator('[data-clerk-element="sign-in"]')).toBeVisible();
    });
  });

  test.describe('Bill Management Workflow', () => {
    test('should create a new bill successfully', async ({ page }) => {
      await loginUser(page);
      
      // Create test patient first
      const patientId = await createTestPatient(page);
      
      // Navigate to billing
      await navigateToBilling(page);
      
      // Click new bill button
      await page.click('button:has-text("新建账单")');
      await page.waitForSelector('[role="dialog"]');
      
      // Fill bill form
      await page.selectOption('select[name="patient"]', patientId);
      await page.selectOption('select[name="billType"]', TEST_BILL.billType);
      await page.fill('textarea[name="description"]', TEST_BILL.description);
      
      // Add bill items
      for (const [index, item] of TEST_BILL.items.entries()) {
        if (index > 0) {
          await page.click('button:has-text("添加项目")');
        }
        
        await page.fill(`input[name="items.${index}.itemName"]`, item.itemName);
        await page.fill(`input[name="items.${index}.quantity"]`, item.quantity.toString());
        await page.fill(`input[name="items.${index}.unitPrice"]`, item.unitPrice.toString());
        await page.fill(`input[name="items.${index}.discountRate"]`, item.discountRate.toString());
      }
      
      // Verify calculated total
      const totalAmount = page.locator('[data-testid="total-amount"]');
      await expect(totalAmount).toHaveText('¥245.00');
      
      // Submit form
      await page.click('button[type="submit"]');
      
      // Wait for success message
      await page.waitForSelector('.toast:has-text("账单创建成功")');
      
      // Verify bill appears in list
      await expect(page.locator(`tr:has-text("${TEST_PATIENT.fullName}")`)).toBeVisible();
      await expect(page.locator('tr:has-text("¥245.00")')).toBeVisible();
      await expect(page.locator('tr:has-text("待支付")')).toBeVisible();
    });

    test('should validate bill form inputs', async ({ page }) => {
      await loginUser(page);
      await navigateToBilling(page);
      
      // Click new bill button
      await page.click('button:has-text("新建账单")');
      await page.waitForSelector('[role="dialog"]');
      
      // Try to submit empty form
      await page.click('button[type="submit"]');
      
      // Verify validation errors
      await expect(page.locator('.error:has-text("患者是必填项")')).toBeVisible();
      await expect(page.locator('.error:has-text("账单类型是必填项")')).toBeVisible();
      await expect(page.locator('.error:has-text("至少需要一个账单项目")')).toBeVisible();
    });

    test('should search and filter bills', async ({ page }) => {
      await loginUser(page);
      await navigateToBilling(page);
      
      // Wait for bills to load
      await page.waitForSelector('[data-testid="bills-list"]');
      
      // Test search functionality
      await page.fill('input[placeholder*="搜索账单"]', TEST_PATIENT.fullName);
      await page.waitForTimeout(500); // Wait for debounced search
      
      // Verify filtered results
      await expect(page.locator(`tr:has-text("${TEST_PATIENT.fullName}")`)).toBeVisible();
      
      // Test status filter
      await page.selectOption('select[name="status"]', 'pending');
      await page.waitForTimeout(500);
      
      // Verify only pending bills are shown
      await expect(page.locator('tr:has-text("待支付")')).toBeVisible();
      await expect(page.locator('tr:has-text("已支付")')).not.toBeVisible();
    });
  });

  test.describe('Payment Processing Workflow', () => {
    test('should process payment for a bill', async ({ page }) => {
      await loginUser(page);
      await navigateToBilling(page);
      
      // Find a pending bill and click pay button
      const billRow = page.locator('tr:has-text("待支付")').first();
      await billRow.locator('button:has-text("支付")').click();
      
      // Wait for payment dialog
      await page.waitForSelector('[role="dialog"]:has-text("处理支付")');
      
      // Fill payment form
      await page.fill('input[name="amount"]', TEST_PAYMENT.amount.toString());
      await page.selectOption('select[name="paymentMethod"]', TEST_PAYMENT.paymentMethod);
      await page.fill('input[name="transactionId"]', TEST_PAYMENT.transactionId);
      await page.fill('textarea[name="notes"]', TEST_PAYMENT.notes);
      
      // Submit payment
      await page.click('button[type="submit"]');
      
      // Wait for success message
      await page.waitForSelector('.toast:has-text("支付处理成功")');
      
      // Verify bill status updated
      await expect(billRow.locator('td:has-text("已支付")')).toBeVisible();
      
      // Verify payment appears in payments list
      await page.click('tab:has-text("收据管理")');
      await expect(page.locator(`tr:has-text("${TEST_PAYMENT.transactionId}")`)).toBeVisible();
      await expect(page.locator(`tr:has-text("¥${TEST_PAYMENT.amount}")`)).toBeVisible();
    });

    test('should validate payment amounts', async ({ page }) => {
      await loginUser(page);
      await navigateToBilling(page);
      
      // Find a bill and click pay button
      const billRow = page.locator('tr:has-text("待支付")').first();
      await billRow.locator('button:has-text("支付")').click();
      
      // Wait for payment dialog
      await page.waitForSelector('[role="dialog"]:has-text("处理支付")');
      
      // Try to pay more than bill amount
      await page.fill('input[name="amount"]', '1000.00');
      await page.selectOption('select[name="paymentMethod"]', 'cash');
      
      // Submit payment
      await page.click('button[type="submit"]');
      
      // Verify validation error
      await expect(page.locator('.error:has-text("支付金额不能超过账单余额")')).toBeVisible();
    });

    test('should handle different payment methods', async ({ page }) => {
      await loginUser(page);
      await navigateToBilling(page);
      
      const paymentMethods = [
        { value: 'cash', label: '现金', requiresTransactionId: false },
        { value: 'card', label: '银行卡', requiresTransactionId: true },
        { value: 'wechat', label: '微信支付', requiresTransactionId: true },
        { value: 'alipay', label: '支付宝', requiresTransactionId: true },
      ];
      
      for (const method of paymentMethods) {
        // Find a pending bill
        const billRow = page.locator('tr:has-text("待支付")').first();
        await billRow.locator('button:has-text("支付")').click();
        
        // Wait for payment dialog
        await page.waitForSelector('[role="dialog"]:has-text("处理支付")');
        
        // Select payment method
        await page.selectOption('select[name="paymentMethod"]', method.value);
        
        // Verify transaction ID field visibility
        const transactionIdField = page.locator('input[name="transactionId"]');
        if (method.requiresTransactionId) {
          await expect(transactionIdField).toBeVisible();
          await expect(transactionIdField).toHaveAttribute('required');
        } else {
          await expect(transactionIdField).not.toBeVisible();
        }
        
        // Close dialog
        await page.click('button:has-text("取消")');
      }
    });
  });

  test.describe('Financial Reports', () => {
    test('should display financial reports', async ({ page }) => {
      await loginUser(page);
      await navigateToBilling(page);
      
      // Navigate to financial reports tab
      await page.click('tab:has-text("财务报表")');
      
      // Verify report sections
      await expect(page.locator('h3:has-text("日收入统计")')).toBeVisible();
      await expect(page.locator('h3:has-text("月收入统计")')).toBeVisible();
      await expect(page.locator('h3:has-text("应收账款")')).toBeVisible();
      
      // Test date range selection
      await page.fill('input[name="dateFrom"]', '2024-01-01');
      await page.fill('input[name="dateTo"]', '2024-12-31');
      await page.click('button:has-text("刷新数据")');
      
      // Wait for data to load
      await page.waitForSelector('[data-testid="financial-data"]');
      
      // Verify data is displayed
      await expect(page.locator('[data-testid="total-income"]')).toBeVisible();
      await expect(page.locator('[data-testid="pending-amount"]')).toBeVisible();
    });
  });

  test.describe('Performance and User Experience', () => {
    test('should load billing page within acceptable time', async ({ page }) => {
      await loginUser(page);
      
      const startTime = Date.now();
      await navigateToBilling(page);
      const loadTime = Date.now() - startTime;
      
      // Verify page loads within 3 seconds
      expect(loadTime).toBeLessThan(3000);
      
      // Verify all essential elements are visible
      await expect(page.locator('h2:has-text("财务管理")')).toBeVisible();
      await expect(page.locator('[data-testid="bills-list"]')).toBeVisible();
    });

    test('should handle large datasets with pagination', async ({ page }) => {
      await loginUser(page);
      await navigateToBilling(page);
      
      // Wait for bills to load
      await page.waitForSelector('[data-testid="bills-list"]');
      
      // Check if pagination is present for large datasets
      const paginationExists = await page.locator('.pagination').isVisible();
      
      if (paginationExists) {
        // Test pagination navigation
        await page.click('button:has-text("下一页")');
        await page.waitForTimeout(1000);
        
        // Verify page changed
        await expect(page.locator('.pagination .active:has-text("2")')).toBeVisible();
        
        // Go back to first page
        await page.click('button:has-text("上一页")');
        await expect(page.locator('.pagination .active:has-text("1")')).toBeVisible();
      }
    });

    test('should provide responsive design on mobile', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      
      await loginUser(page);
      await navigateToBilling(page);
      
      // Verify mobile-friendly layout
      await expect(page.locator('.sidebar')).not.toBeVisible(); // Sidebar should be hidden
      await expect(page.locator('button[aria-label="Toggle Sidebar"]')).toBeVisible();
      
      // Test mobile navigation
      await page.click('button[aria-label="Toggle Sidebar"]');
      await expect(page.locator('.sidebar')).toBeVisible();
      
      // Verify billing content is accessible
      await expect(page.locator('h2:has-text("财务管理")')).toBeVisible();
    });
  });

  test.describe('Error Handling', () => {
    test('should handle network errors gracefully', async ({ page }) => {
      await loginUser(page);
      
      // Intercept API requests and simulate network error
      await page.route('**/api/bills', route => {
        route.abort('failed');
      });
      
      await navigateToBilling(page);
      
      // Verify error message is displayed
      await expect(page.locator('.error:has-text("网络连接错误")')).toBeVisible();
      
      // Verify retry functionality
      await page.unroute('**/api/bills');
      await page.click('button:has-text("重试")');
      
      // Verify data loads after retry
      await page.waitForSelector('[data-testid="bills-list"]');
    });

    test('should handle server errors appropriately', async ({ page }) => {
      await loginUser(page);
      
      // Intercept API requests and simulate server error
      await page.route('**/api/bills', route => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({
            error: 'Internal server error',
            code: 'INTERNAL_ERROR',
            message: '服务器内部错误，请稍后重试',
          }),
        });
      });
      
      await navigateToBilling(page);
      
      // Verify error message is displayed
      await expect(page.locator('.error:has-text("服务器内部错误")')).toBeVisible();
    });
  });

  test.describe('Security', () => {
    test('should prevent unauthorized access to billing data', async ({ page }) => {
      // Try to access billing page without authentication
      await page.goto(`${BASE_URL}/dashboard/billing`);
      
      // Should redirect to sign-in
      await page.waitForURL(/.*sign-in.*/);
      await expect(page.locator('[data-clerk-element="sign-in"]')).toBeVisible();
    });

    test('should sanitize user inputs', async ({ page }) => {
      await loginUser(page);
      await navigateToBilling(page);
      
      // Try to inject script in bill description
      await page.click('button:has-text("新建账单")');
      await page.waitForSelector('[role="dialog"]');
      
      const maliciousInput = '<script>alert("XSS")</script>';
      await page.fill('textarea[name="description"]', maliciousInput);
      
      // Verify input is sanitized (script tags removed)
      const sanitizedValue = await page.inputValue('textarea[name="description"]');
      expect(sanitizedValue).not.toContain('<script>');
      expect(sanitizedValue).not.toContain('alert');
    });
  });
});
