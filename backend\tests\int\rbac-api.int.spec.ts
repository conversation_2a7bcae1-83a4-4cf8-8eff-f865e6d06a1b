import { describe, it, beforeAll, afterAll, expect } from 'vitest'
import request from 'supertest'
import { getPayload, Payload } from 'payload'
import config from '@/payload.config'

let payload: Payload
let adminUser: any
let doctorUser: any
let frontDeskUser: any
let testPatient: any
let testBill: any

// Mock authentication headers for different user roles
const createAuthHeaders = (user: any) => ({
  'x-user-id': user.id,
  'x-user-email': user.email,
  'x-user-role': user.role,
  'x-user-first-name': user.firstName,
  'x-user-last-name': user.lastName,
})

describe('RBAC API Endpoints Integration Tests', () => {
  beforeAll(async () => {
    const payloadConfig = await config
    payload = await getPayload({ config: payloadConfig })

    // Create test users with different roles
    adminUser = await payload.create({
      collection: 'users',
      data: {
        email: `admin-api-${Date.now()}@clinic.com`,
        role: 'admin',
        firstName: 'Admin',
        lastName: 'API',
        clerkId: `clerk_admin_api_${Date.now()}`,
      },
    })

    doctorUser = await payload.create({
      collection: 'users',
      data: {
        email: `doctor-api-${Date.now()}@clinic.com`,
        role: 'doctor',
        firstName: 'Doctor',
        lastName: 'API',
        clerkId: `clerk_doctor_api_${Date.now()}`,
      },
    })

    frontDeskUser = await payload.create({
      collection: 'users',
      data: {
        email: `frontdesk-api-${Date.now()}@clinic.com`,
        role: 'front-desk',
        firstName: 'FrontDesk',
        lastName: 'API',
        clerkId: `clerk_frontdesk_api_${Date.now()}`,
      },
    })

    // Create test patient
    testPatient = await payload.create({
      collection: 'patients',
      data: {
        fullName: 'API RBAC测试患者',
        phone: `138${Date.now().toString().slice(-8)}`,
        email: `rbac-api-patient-${Date.now()}@test.com`,
        medicalNotes: 'API RBAC权限测试',
      },
    })

    // Create test bill
    testBill = await payload.create({
      collection: 'bills',
      data: {
        patient: testPatient.id,
        billType: 'treatment',
        subtotal: 1000,
        totalAmount: 1000,
        status: 'confirmed',
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        createdBy: adminUser.id,
      },
    })
  })

  afterAll(async () => {
    // Clean up test data
    if (testBill) await payload.delete({ collection: 'bills', id: testBill.id }).catch(() => {})
    if (testPatient) await payload.delete({ collection: 'patients', id: testPatient.id }).catch(() => {})
    if (frontDeskUser) await payload.delete({ collection: 'users', id: frontDeskUser.id }).catch(() => {})
    if (doctorUser) await payload.delete({ collection: 'users', id: doctorUser.id }).catch(() => {})
    if (adminUser) await payload.delete({ collection: 'users', id: adminUser.id }).catch(() => {})
  })

  describe('Financial Reports API RBAC', () => {
    it('Admin should have access to all financial reports', async () => {
      const app = (global as any).testApp
      
      // Test daily revenue report
      const dailyResponse = await request(app)
        .get('/api/reports/daily-revenue?date=2024-01-01')
        .set(createAuthHeaders(adminUser))
        .expect(200)
      
      expect(dailyResponse.body.success).toBe(true)
      expect(dailyResponse.body.totalRevenue).toBeDefined()

      // Test monthly revenue report
      const monthlyResponse = await request(app)
        .get('/api/reports/monthly-revenue?year=2024&month=1')
        .set(createAuthHeaders(adminUser))
        .expect(200)
      
      expect(monthlyResponse.body.success).toBe(true)
      expect(monthlyResponse.body.totalRevenue).toBeDefined()

      // Test outstanding balances report
      const balancesResponse = await request(app)
        .get('/api/reports/outstanding-balances')
        .set(createAuthHeaders(adminUser))
        .expect(200)
      
      expect(balancesResponse.body.success).toBe(true)
      expect(balancesResponse.body.totalOutstanding).toBeDefined()
    })

    it('Front-desk should have access to financial reports', async () => {
      const app = (global as any).testApp
      
      // Test daily revenue report
      const dailyResponse = await request(app)
        .get('/api/reports/daily-revenue?date=2024-01-01')
        .set(createAuthHeaders(frontDeskUser))
        .expect(200)
      
      expect(dailyResponse.body.success).toBe(true)

      // Test outstanding balances report
      const balancesResponse = await request(app)
        .get('/api/reports/outstanding-balances')
        .set(createAuthHeaders(frontDeskUser))
        .expect(200)
      
      expect(balancesResponse.body.success).toBe(true)
    })

    it('Doctor should NOT have access to financial reports', async () => {
      const app = (global as any).testApp
      
      // Test daily revenue report - should be forbidden
      await request(app)
        .get('/api/reports/daily-revenue?date=2024-01-01')
        .set(createAuthHeaders(doctorUser))
        .expect(403)

      // Test monthly revenue report - should be forbidden
      await request(app)
        .get('/api/reports/monthly-revenue?year=2024&month=1')
        .set(createAuthHeaders(doctorUser))
        .expect(403)

      // Test outstanding balances report - should be forbidden
      await request(app)
        .get('/api/reports/outstanding-balances')
        .set(createAuthHeaders(doctorUser))
        .expect(403)
    })
  })

  describe('Deposit Refund API RBAC', () => {
    let testDeposit: any

    beforeAll(async () => {
      // Create test deposit
      testDeposit = await payload.create({
        collection: 'deposits',
        data: {
          patient: testPatient.id,
          amount: 1000.00,
          status: 'active',
          receivedBy: adminUser.id,
          notes: 'API RBAC退款测试',
        },
      })
    })

    afterAll(async () => {
      if (testDeposit) await payload.delete({ collection: 'deposits', id: testDeposit.id }).catch(() => {})
    })

    it('Admin should be able to process deposit refunds', async () => {
      const app = (global as any).testApp
      
      const refundResponse = await request(app)
        .post('/api/deposits/refund')
        .set(createAuthHeaders(adminUser))
        .send({
          depositId: testDeposit.id,
          refundAmount: 500.00,
          refundReason: 'API测试退款',
          refundMethod: 'cash'
        })
        .expect(200)
      
      expect(refundResponse.body.success).toBe(true)
      expect(refundResponse.body.refund.amount).toBe(500.00)
    })

    it('Doctor should NOT be able to process deposit refunds', async () => {
      const app = (global as any).testApp
      
      await request(app)
        .post('/api/deposits/refund')
        .set(createAuthHeaders(doctorUser))
        .send({
          depositId: testDeposit.id,
          refundAmount: 100.00,
          refundReason: 'Doctor测试退款',
          refundMethod: 'cash'
        })
        .expect(403)
    })

    it('Front-desk should NOT be able to process deposit refunds', async () => {
      const app = (global as any).testApp
      
      await request(app)
        .post('/api/deposits/refund')
        .set(createAuthHeaders(frontDeskUser))
        .send({
          depositId: testDeposit.id,
          refundAmount: 100.00,
          refundReason: 'Front-desk测试退款',
          refundMethod: 'cash'
        })
        .expect(403)
    })
  })

  describe('Authentication Required Tests', () => {
    it('Should require authentication for all financial endpoints', async () => {
      const app = (global as any).testApp
      
      // Test without auth headers
      await request(app)
        .get('/api/reports/daily-revenue?date=2024-01-01')
        .expect(401)

      await request(app)
        .post('/api/deposits/refund')
        .send({
          depositId: testDeposit?.id || 'test',
          refundAmount: 100.00,
          refundReason: '无认证测试',
          refundMethod: 'cash'
        })
        .expect(401)
    })

    it('Should validate required parameters', async () => {
      const app = (global as any).testApp
      
      // Test daily revenue without date parameter
      await request(app)
        .get('/api/reports/daily-revenue')
        .set(createAuthHeaders(adminUser))
        .expect(400)

      // Test monthly revenue without required parameters
      await request(app)
        .get('/api/reports/monthly-revenue?year=2024')
        .set(createAuthHeaders(adminUser))
        .expect(400)

      // Test refund without required fields
      await request(app)
        .post('/api/deposits/refund')
        .set(createAuthHeaders(adminUser))
        .send({
          depositId: 'test'
          // Missing refundAmount and refundReason
        })
        .expect(400)
    })
  })

  describe('Data Validation Tests', () => {
    it('Should validate date formats in reports', async () => {
      const app = (global as any).testApp
      
      // Test invalid date format
      await request(app)
        .get('/api/reports/daily-revenue?date=invalid-date')
        .set(createAuthHeaders(adminUser))
        .expect(400)

      // Test invalid month
      await request(app)
        .get('/api/reports/monthly-revenue?year=2024&month=13')
        .set(createAuthHeaders(adminUser))
        .expect(400)
    })

    it('Should validate refund amounts', async () => {
      const app = (global as any).testApp
      
      // Test negative refund amount
      await request(app)
        .post('/api/deposits/refund')
        .set(createAuthHeaders(adminUser))
        .send({
          depositId: testDeposit?.id || 'test',
          refundAmount: -100.00,
          refundReason: '负数测试',
          refundMethod: 'cash'
        })
        .expect(400)

      // Test zero refund amount
      await request(app)
        .post('/api/deposits/refund')
        .set(createAuthHeaders(adminUser))
        .send({
          depositId: testDeposit?.id || 'test',
          refundAmount: 0,
          refundReason: '零值测试',
          refundMethod: 'cash'
        })
        .expect(400)
    })
  })
})
