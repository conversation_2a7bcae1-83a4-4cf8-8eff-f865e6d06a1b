# 账单系统 API 参考文档

## 概述

本文档提供了医疗诊所账单管理系统所有API端点的详细参考信息。

**基础URL**: `http://localhost:3000/api` (开发环境)  
**认证方式**: Clerk JWT Token  
**内容类型**: `application/json`

## 认证

所有API请求都需要有效的Clerk认证令牌。前端会自动处理认证，但如果直接调用API，需要在请求头中包含：

```http
Authorization: Bearer <clerk-jwt-token>
```

## 错误响应格式

所有错误响应都遵循统一格式：

```json
{
  "error": "错误描述",
  "code": "ERROR_CODE",
  "message": "用户友好的错误信息",
  "details": {} // 可选的详细错误信息
}
```

## 账单 API (Bills)

### GET /api/bills

获取账单列表，支持分页和筛选。

**查询参数:**
| 参数 | 类型 | 必需 | 默认值 | 描述 |
|------|------|------|--------|------|
| page | number | 否 | 1 | 页码 |
| limit | number | 否 | 20 | 每页数量 (最大100) |
| search | string | 否 | - | 搜索关键词 |
| status | string | 否 | - | 账单状态 |
| billType | string | 否 | - | 账单类型 |
| patientId | string | 否 | - | 患者ID |
| dateFrom | string | 否 | - | 开始日期 (YYYY-MM-DD) |
| dateTo | string | 否 | - | 结束日期 (YYYY-MM-DD) |

**响应示例:**
```json
{
  "docs": [
    {
      "id": "bill-123",
      "billNumber": "BILL-001",
      "patient": {
        "id": "patient-123",
        "fullName": "张三"
      },
      "billType": "consultation",
      "description": "医疗咨询",
      "items": [
        {
          "id": "item-1",
          "itemName": "咨询费",
          "quantity": 1,
          "unitPrice": 200.00,
          "discountRate": 0,
          "totalAmount": 200.00
        }
      ],
      "subtotal": 200.00,
      "discountAmount": 0.00,
      "taxAmount": 0.00,
      "totalAmount": 200.00,
      "paidAmount": 0.00,
      "remainingAmount": 200.00,
      "status": "pending",
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z",
      "dueDate": "2024-01-31T00:00:00Z"
    }
  ],
  "totalDocs": 1,
  "page": 1,
  "limit": 20,
  "totalPages": 1,
  "hasNext": false,
  "hasPrev": false
}
```

### POST /api/bills

创建新账单。

**请求体:**
```json
{
  "patient": "patient-123",
  "billType": "consultation",
  "description": "医疗咨询",
  "items": [
    {
      "itemName": "咨询费",
      "quantity": 1,
      "unitPrice": 200.00,
      "discountRate": 0
    }
  ],
  "dueDate": "2024-01-31",
  "notes": "备注信息"
}
```

**响应:** 返回创建的账单对象 (201 Created)

### GET /api/bills/{id}

获取特定账单的详细信息。

**路径参数:**
- `id`: 账单ID

**响应:** 返回账单对象

### PATCH /api/bills/{id}

更新账单信息。

**路径参数:**
- `id`: 账单ID

**请求体:** 部分账单对象 (只需包含要更新的字段)

**响应:** 返回更新后的账单对象

### DELETE /api/bills/{id}

删除账单。

**路径参数:**
- `id`: 账单ID

**响应:** 204 No Content

## 支付 API (Payments)

### GET /api/payments

获取支付记录列表。

**查询参数:**
| 参数 | 类型 | 必需 | 默认值 | 描述 |
|------|------|------|--------|------|
| page | number | 否 | 1 | 页码 |
| limit | number | 否 | 20 | 每页数量 |
| billId | string | 否 | - | 账单ID |
| paymentMethod | string | 否 | - | 支付方式 |
| dateFrom | string | 否 | - | 开始日期 |
| dateTo | string | 否 | - | 结束日期 |

### POST /api/payments

创建新支付记录。

**请求体:**
```json
{
  "billId": "bill-123",
  "amount": 200.00,
  "paymentMethod": "cash",
  "transactionId": "TXN-001",
  "notes": "现金支付"
}
```

**支付方式枚举:**
- `cash`: 现金
- `card`: 银行卡
- `wechat`: 微信支付
- `alipay`: 支付宝
- `transfer`: 银行转账
- `installment`: 分期付款

**响应:** 返回创建的支付记录 (201 Created)

### GET /api/payments/{id}

获取特定支付记录。

### PATCH /api/payments/{id}

更新支付记录。

## 账单项目 API (Bill Items)

### GET /api/bill-items

获取账单项目列表。

**查询参数:**
- `billId`: 账单ID (筛选特定账单的项目)

### POST /api/bill-items

创建账单项目。

**请求体:**
```json
{
  "billId": "bill-123",
  "itemName": "检查费",
  "quantity": 1,
  "unitPrice": 150.00,
  "discountRate": 10
}
```

### PATCH /api/bill-items/{id}

更新账单项目。

### DELETE /api/bill-items/{id}

删除账单项目。

## 预付款 API (Deposits)

### GET /api/deposits

获取预付款记录。

### POST /api/deposits

创建预付款记录。

**请求体:**
```json
{
  "patientId": "patient-123",
  "amount": 500.00,
  "paymentMethod": "card",
  "transactionId": "DEP-001",
  "notes": "预付款"
}
```

## 特殊端点

### POST /api/bills/generate-from-appointment

从预约生成账单。

**请求体:**
```json
{
  "appointmentId": "appointment-123",
  "billType": "consultation",
  "items": [
    {
      "itemName": "咨询费",
      "quantity": 1,
      "unitPrice": 200.00
    }
  ]
}
```

### POST /api/deposits/apply-to-bill

将预付款应用到账单。

**请求体:**
```json
{
  "depositId": "deposit-123",
  "billId": "bill-123",
  "amount": 100.00
}
```

## 状态码

| 状态码 | 描述 |
|--------|------|
| 200 | 成功 |
| 201 | 创建成功 |
| 204 | 删除成功 |
| 400 | 请求错误 |
| 401 | 未认证 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 429 | 请求过于频繁 |
| 500 | 服务器错误 |
| 503 | 服务不可用 |

## 错误代码

| 错误代码 | 描述 |
|----------|------|
| AUTH_REQUIRED | 需要认证 |
| VALIDATION_ERROR | 数据验证失败 |
| INVALID_JSON | JSON格式错误 |
| RATE_LIMIT_EXCEEDED | 超过频率限制 |
| PAYMENT_RATE_LIMIT_EXCEEDED | 超过支付频率限制 |
| BILL_NOT_FOUND | 账单不存在 |
| INVALID_PAYMENT_AMOUNT | 支付金额无效 |
| SUBTOTAL_MISMATCH | 小计金额不匹配 |
| BACKEND_SERVICE_ERROR | 后端服务错误 |

## 使用示例

### JavaScript/TypeScript

```typescript
// 获取账单列表
const response = await fetch('/api/bills?page=1&limit=10', {
  headers: {
    'Content-Type': 'application/json',
  },
});
const bills = await response.json();

// 创建账单
const newBill = await fetch('/api/bills', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    patient: 'patient-123',
    billType: 'consultation',
    items: [
      {
        itemName: '咨询费',
        quantity: 1,
        unitPrice: 200.00,
        discountRate: 0
      }
    ]
  }),
});

// 处理支付
const payment = await fetch('/api/payments', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    billId: 'bill-123',
    amount: 200.00,
    paymentMethod: 'cash',
    transactionId: 'CASH-001'
  }),
});
```

### cURL

```bash
# 获取账单列表
curl -X GET "http://localhost:3000/api/bills?page=1&limit=10" \
  -H "Content-Type: application/json"

# 创建账单
curl -X POST "http://localhost:3000/api/bills" \
  -H "Content-Type: application/json" \
  -d '{
    "patient": "patient-123",
    "billType": "consultation",
    "items": [
      {
        "itemName": "咨询费",
        "quantity": 1,
        "unitPrice": 200.00,
        "discountRate": 0
      }
    ]
  }'
```

## 频率限制

- **常规API请求**: 每分钟60次
- **支付相关请求**: 每小时10次

超过限制时会返回429状态码和重置时间。

## 版本信息

**API版本**: v2.0  
**最后更新**: 2024年12月  
**兼容性**: 向后兼容v1.0
