const CHUNK_PUBLIC_PATH = "server/app/api/payments/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules__pnpm_45e2fd78._.js");
runtime.loadChunk("server/chunks/c64f8_next_371d3e4c._.js");
runtime.loadChunk("server/chunks/a5167_@clerk_backend_dist_b67e932e._.js");
runtime.loadChunk("server/chunks/05b4d_zod_lib_index_mjs_17c65e86._.js");
runtime.loadChunk("server/chunks/node_modules__pnpm_6dabd9b8._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__60ab0aab._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/payments/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/payments/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/payments/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
