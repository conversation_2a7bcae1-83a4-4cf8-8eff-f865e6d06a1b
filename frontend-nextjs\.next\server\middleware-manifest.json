{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/node_modules__pnpm_f70aea8e._.js", "server/edge/chunks/[root-of-the-server]__92ca1197._.js", "server/edge/chunks/edge-wrapper_26561c45.js", "server/edge/chunks/_14c1c4d5._.js", "server/edge/chunks/a5167_@clerk_backend_dist_a1e27434._.js", "server/edge/chunks/79fbd_@clerk_shared_dist_f7e95f9b._.js", "server/edge/chunks/4e4c4_@clerk_nextjs_dist_esm_4e5142ff._.js", "server/edge/chunks/node_modules__pnpm_fac582cc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_5ff26227.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "6iMNFxJQRw5kwyobQ2E/Q8u2QleJW6yuaZTizsNE3Gw=", "__NEXT_PREVIEW_MODE_ID": "a1739ed2184bcb80145a4e362d95891a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "0eddd36ca65cb20bde5a6b128d6e90fca984d8184d1d60cf715dca3495ee2279", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "059d0892fcb86994a32a0fd5cb0e40aed41dfa01305d1872a76bc54fb4b8988d"}}}, "sortedMiddleware": ["/"], "functions": {}}