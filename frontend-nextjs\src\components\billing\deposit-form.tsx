'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  IconCash, 
  IconCalendar,
  IconUser,
  IconX,
  IconDeviceFloppy
} from '@tabler/icons-react';
import { Patient } from '@/types/clinic';
import { depositsAPI, BillingAPIError, billingUtils } from '@/lib/api/billing';
import { toast } from 'sonner';
import { billingNotifications } from '@/lib/billing-notifications';

// Deposit form validation schema
const depositSchema = z.object({
  patient: z.string().min(1, '请选择患者'),
  depositType: z.enum(['treatment', 'appointment', 'material'], {
    required_error: '请选择押金类型',
  }),
  amount: z.number().min(0.01, '押金金额必须大于0'),
  purpose: z.string().min(1, '请输入押金用途'),
  notes: z.string().optional(),
  expiryDate: z.string().optional(),
});

type DepositFormData = z.infer<typeof depositSchema>;

interface DepositFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  patients: Patient[];
  selectedPatient?: Patient;
  onSuccess?: () => void;
}

export function DepositForm({
  open,
  onOpenChange,
  patients,
  selectedPatient,
  onSuccess,
}: DepositFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<DepositFormData>({
    resolver: zodResolver(depositSchema),
    defaultValues: {
      patient: selectedPatient?.id || '',
      depositType: 'treatment',
      amount: 0,
      purpose: '',
      notes: '',
      expiryDate: '',
    },
  });

  const handleSubmit = async (data: DepositFormData) => {
    setIsSubmitting(true);
    try {
      await depositsAPI.createDeposit({
        ...data,
        expiryDate: data.expiryDate || undefined,
      });

      toast.success(billingNotifications.deposit.created);
      form.reset();
      onOpenChange(false);
      onSuccess?.();
    } catch (error) {
      console.error('Error creating deposit:', error);
      if (error instanceof BillingAPIError) {
        toast.error(error.message);
      } else {
        toast.error(billingNotifications.deposit.createError);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const depositTypeOptions = [
    { value: 'treatment', label: '治疗押金' },
    { value: 'appointment', label: '预约押金' },
    { value: 'material', label: '材料押金' },
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <IconCash className="h-5 w-5" />
            创建押金记录
          </DialogTitle>
          <DialogDescription>
            为患者创建新的押金记录，用于后续治疗或预约抵扣
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Patient Selection */}
              <FormField
                control={form.control}
                name="patient"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <IconUser className="h-4 w-4" />
                      患者
                    </FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="选择患者" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {patients.map((patient) => (
                          <SelectItem key={patient.id} value={patient.id}>
                            {patient.fullName} - {patient.phone}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Deposit Type */}
              <FormField
                control={form.control}
                name="depositType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>押金类型</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="选择押金类型" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {depositTypeOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Amount */}
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>押金金额</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="0.00"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormDescription>
                      押金金额（人民币）
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Expiry Date */}
              <FormField
                control={form.control}
                name="expiryDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <IconCalendar className="h-4 w-4" />
                      到期日期（可选）
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="date"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      押金到期日期，留空表示无期限
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Purpose */}
            <FormField
              control={form.control}
              name="purpose"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>押金用途</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="例如：激光治疗押金、年度套餐预付款等"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    简要说明此押金的用途
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>备注（可选）</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="押金相关的备注信息..."
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Separator />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
              >
                <IconX className="h-4 w-4 mr-2" />
                取消
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                <IconDeviceFloppy className="h-4 w-4 mr-2" />
                {isSubmitting ? '创建中...' : '创建押金'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
