'use client';

// Security monitoring dashboard for billing system
// Displays audit logs, security alerts, and system health

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  IconShield, 
  IconAlertTriangle, 
  IconEye, 
  IconClock,
  IconUser,
  IconActivity,
  IconLock,
  IconRefresh
} from '@tabler/icons-react';
import { auditLogger, rateLimiter } from '@/lib/billing-security';
import type { AuditLogEntry } from '@/lib/billing-security';

interface SecurityMonitorProps {
  className?: string;
  showFullDetails?: boolean;
}

export function SecurityMonitor({ className = '', showFullDetails = false }: SecurityMonitorProps) {
  const [auditLogs, setAuditLogs] = useState<AuditLogEntry[]>([]);
  const [securityAlerts, setSecurityAlerts] = useState<SecurityAlert[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  // Security alert interface
  interface SecurityAlert {
    id: string;
    type: 'warning' | 'error' | 'info';
    title: string;
    message: string;
    timestamp: Date;
    resolved: boolean;
  }

  // Load security data
  const loadSecurityData = async () => {
    setIsLoading(true);
    try {
      // Get audit logs
      const logs = auditLogger.getAuditLog(undefined, 50);
      setAuditLogs(logs);

      // Generate security alerts based on audit logs
      const alerts = generateSecurityAlerts(logs);
      setSecurityAlerts(alerts);

      setLastRefresh(new Date());
    } catch (error) {
      console.error('Failed to load security data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Generate security alerts from audit logs
  const generateSecurityAlerts = (logs: AuditLogEntry[]): SecurityAlert[] => {
    const alerts: SecurityAlert[] = [];
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

    // Check for failed authentication attempts
    const failedAuthAttempts = logs.filter(log => 
      log.action.includes('UNAUTHORIZED') && 
      log.timestamp > oneHourAgo
    );

    if (failedAuthAttempts.length > 5) {
      alerts.push({
        id: 'failed-auth',
        type: 'warning',
        title: '多次认证失败',
        message: `过去一小时内检测到 ${failedAuthAttempts.length} 次认证失败尝试`,
        timestamp: now,
        resolved: false
      });
    }

    // Check for rate limiting incidents
    const rateLimitedRequests = logs.filter(log => 
      log.action.includes('RATE_LIMITED') && 
      log.timestamp > oneHourAgo
    );

    if (rateLimitedRequests.length > 0) {
      alerts.push({
        id: 'rate-limited',
        type: 'info',
        title: '频率限制触发',
        message: `过去一小时内 ${rateLimitedRequests.length} 个请求被频率限制`,
        timestamp: now,
        resolved: false
      });
    }

    // Check for validation failures
    const validationFailures = logs.filter(log => 
      log.action.includes('VALIDATION_FAILED') && 
      log.timestamp > oneHourAgo
    );

    if (validationFailures.length > 10) {
      alerts.push({
        id: 'validation-failures',
        type: 'warning',
        title: '大量验证失败',
        message: `过去一小时内检测到 ${validationFailures.length} 次数据验证失败`,
        timestamp: now,
        resolved: false
      });
    }

    return alerts;
  };

  // Load data on component mount
  useEffect(() => {
    loadSecurityData();
    
    // Set up periodic refresh
    const interval = setInterval(loadSecurityData, 30000); // Refresh every 30 seconds
    
    return () => clearInterval(interval);
  }, []);

  // Get action display name
  const getActionDisplayName = (action: string): string => {
    const actionNames: Record<string, string> = {
      GET_BILLS: '查看账单',
      CREATE_BILL: '创建账单',
      UPDATE_BILL: '更新账单',
      DELETE_BILL: '删除账单',
      CREATE_PAYMENT: '创建支付',
      GET_BILLS_UNAUTHORIZED: '未授权访问账单',
      CREATE_PAYMENT_UNAUTHORIZED: '未授权创建支付',
      GET_BILLS_RATE_LIMITED: '账单查询频率限制',
      CREATE_PAYMENT_RATE_LIMITED: '支付创建频率限制',
      CREATE_BILL_VALIDATION_FAILED: '账单验证失败',
      CREATE_PAYMENT_VALIDATION_FAILED: '支付验证失败',
    };
    return actionNames[action] || action;
  };

  // Get status badge color
  const getStatusBadgeColor = (success: boolean): string => {
    return success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
  };

  // Get alert badge color
  const getAlertBadgeColor = (type: string): string => {
    switch (type) {
      case 'error': return 'bg-red-100 text-red-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'info': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <IconShield className="h-6 w-6 text-blue-600" />
          <h2 className="text-2xl font-bold">安全监控</h2>
        </div>
        
        <div className="flex items-center space-x-4">
          <span className="text-sm text-gray-500">
            最后更新: {lastRefresh.toLocaleTimeString()}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={loadSecurityData}
            disabled={isLoading}
          >
            <IconRefresh className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
        </div>
      </div>

      {/* Security Alerts */}
      {securityAlerts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <IconAlertTriangle className="h-5 w-5 text-yellow-600" />
              <span>安全警报</span>
            </CardTitle>
            <CardDescription>
              系统检测到的安全相关事件
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {securityAlerts.map((alert) => (
                <Alert key={alert.id} className="border-l-4 border-l-yellow-500">
                  <AlertDescription>
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="flex items-center space-x-2">
                          <Badge className={getAlertBadgeColor(alert.type)}>
                            {alert.type === 'error' ? '错误' : alert.type === 'warning' ? '警告' : '信息'}
                          </Badge>
                          <span className="font-medium">{alert.title}</span>
                        </div>
                        <p className="text-sm text-gray-600 mt-1">{alert.message}</p>
                      </div>
                      <span className="text-xs text-gray-500">
                        {alert.timestamp.toLocaleTimeString()}
                      </span>
                    </div>
                  </AlertDescription>
                </Alert>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Security Tabs */}
      <Tabs defaultValue="audit-log" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="audit-log" className="flex items-center space-x-2">
            <IconEye className="h-4 w-4" />
            <span>审计日志</span>
          </TabsTrigger>
          <TabsTrigger value="activity" className="flex items-center space-x-2">
            <IconActivity className="h-4 w-4" />
            <span>活动统计</span>
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center space-x-2">
            <IconLock className="h-4 w-4" />
            <span>安全设置</span>
          </TabsTrigger>
        </TabsList>

        {/* Audit Log Tab */}
        <TabsContent value="audit-log">
          <Card>
            <CardHeader>
              <CardTitle>审计日志</CardTitle>
              <CardDescription>
                财务系统操作的详细记录
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  <span className="ml-2">加载中...</span>
                </div>
              ) : auditLogs.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  暂无审计日志
                </div>
              ) : (
                <div className="space-y-3">
                  {auditLogs.map((log) => (
                    <div key={log.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-3">
                          <Badge className={getStatusBadgeColor(log.success)}>
                            {log.success ? '成功' : '失败'}
                          </Badge>
                          <span className="font-medium">
                            {getActionDisplayName(log.action)}
                          </span>
                          <span className="text-sm text-gray-500">
                            {log.resource}
                          </span>
                        </div>
                        <span className="text-xs text-gray-500">
                          {log.timestamp.toLocaleString()}
                        </span>
                      </div>
                      
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <div className="flex items-center space-x-1">
                          <IconUser className="h-3 w-3" />
                          <span>{log.userEmail}</span>
                        </div>
                        {log.ipAddress && (
                          <div className="flex items-center space-x-1">
                            <IconClock className="h-3 w-3" />
                            <span>{log.ipAddress}</span>
                          </div>
                        )}
                      </div>

                      {!log.success && log.errorMessage && (
                        <div className="mt-2 text-sm text-red-600">
                          错误: {log.errorMessage}
                        </div>
                      )}

                      {showFullDetails && log.details && (
                        <details className="mt-2">
                          <summary className="text-sm text-gray-500 cursor-pointer">
                            查看详细信息
                          </summary>
                          <pre className="mt-2 text-xs bg-gray-50 p-2 rounded overflow-x-auto">
                            {JSON.stringify(log.details, null, 2)}
                          </pre>
                        </details>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Activity Statistics Tab */}
        <TabsContent value="activity">
          <Card>
            <CardHeader>
              <CardTitle>活动统计</CardTitle>
              <CardDescription>
                系统活动的统计信息
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {auditLogs.filter(log => log.success).length}
                  </div>
                  <div className="text-sm text-blue-600">成功操作</div>
                </div>
                
                <div className="bg-red-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-red-600">
                    {auditLogs.filter(log => !log.success).length}
                  </div>
                  <div className="text-sm text-red-600">失败操作</div>
                </div>
                
                <div className="bg-yellow-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-yellow-600">
                    {securityAlerts.length}
                  </div>
                  <div className="text-sm text-yellow-600">安全警报</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security Settings Tab */}
        <TabsContent value="security">
          <Card>
            <CardHeader>
              <CardTitle>安全设置</CardTitle>
              <CardDescription>
                系统安全配置和状态
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                  <div>
                    <div className="font-medium text-green-800">审计日志</div>
                    <div className="text-sm text-green-600">已启用</div>
                  </div>
                  <Badge className="bg-green-100 text-green-800">正常</Badge>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                  <div>
                    <div className="font-medium text-green-800">频率限制</div>
                    <div className="text-sm text-green-600">已启用</div>
                  </div>
                  <Badge className="bg-green-100 text-green-800">正常</Badge>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                  <div>
                    <div className="font-medium text-green-800">输入验证</div>
                    <div className="text-sm text-green-600">已启用</div>
                  </div>
                  <Badge className="bg-green-100 text-green-800">正常</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
