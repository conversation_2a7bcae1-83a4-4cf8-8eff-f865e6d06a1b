import { NextRequest, NextResponse } from 'next/server'
import { authenticateWithPayload, makeAuthenticatedPayloadRequest } from '../../../../lib/payload-auth-middleware'

/**
 * GET /api/reports/outstanding-balances - Generate outstanding balances report
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check permissions - only admin and front-desk can view financial reports
    if (!['admin', 'front-desk'].includes(authContext.user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view financial reports' },
        { status: 403 }
      );
    }

    // Get all unpaid or partially paid bills
    const bills = await makeAuthenticatedPayloadRequest(
      authContext,
      'bills',
      'find',
      {
        where: {
          or: [
            { status: { equals: 'sent' } },
            { status: { equals: 'confirmed' } },
            {
              and: [
                { status: { equals: 'paid' } },
                { remainingAmount: { greater_than: 0 } }
              ]
            }
          ]
        },
        limit: 1000,
        depth: 2,
      }
    );

    const today = new Date();
    let totalOutstanding = 0;
    let overdueAmount = 0;
    let overdueBillsCount = 0;
    const outstandingBills: Array<{
      id: string;
      billNumber: string;
      patient: string;
      amount: number;
      dueDate: string;
      daysOverdue: number;
      status: string;
    }> = [];

    (bills as any).docs.forEach((bill: any) => {
      const remainingAmount = bill.remainingAmount || (bill.totalAmount - (bill.paidAmount || 0));
      
      if (remainingAmount > 0) {
        totalOutstanding += remainingAmount;
        
        const dueDate = bill.dueDate ? new Date(bill.dueDate) : null;
        const isOverdue = dueDate && dueDate < today;
        const daysOverdue = isOverdue ? Math.ceil((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24)) : 0;
        
        if (isOverdue) {
          overdueAmount += remainingAmount;
          overdueBillsCount++;
        }

        // Get patient name
        let patientName = 'Unknown Patient';
        if (typeof bill.patient === 'object' && bill.patient?.fullName) {
          patientName = bill.patient.fullName;
        }

        outstandingBills.push({
          id: bill.id,
          billNumber: bill.billNumber,
          patient: patientName,
          amount: remainingAmount,
          dueDate: dueDate ? dueDate.toISOString().split('T')[0] : '',
          daysOverdue,
          status: bill.status,
        });
      }
    });

    // Sort by days overdue (most overdue first), then by amount (highest first)
    outstandingBills.sort((a, b) => {
      if (a.daysOverdue !== b.daysOverdue) {
        return b.daysOverdue - a.daysOverdue;
      }
      return b.amount - a.amount;
    });

    const reportData = {
      totalOutstanding,
      overdueAmount,
      billsCount: outstandingBills.length,
      overdueBillsCount,
      overduePercentage: totalOutstanding > 0 ? ((overdueAmount / totalOutstanding) * 100).toFixed(2) : 0,
      bills: outstandingBills,
      summary: {
        totalBillsWithBalance: outstandingBills.length,
        averageOutstandingAmount: outstandingBills.length > 0 ? (totalOutstanding / outstandingBills.length).toFixed(2) : 0,
        oldestOverdueDays: outstandingBills.length > 0 ? Math.max(...outstandingBills.map(b => b.daysOverdue)) : 0,
      },
      generatedAt: new Date().toISOString(),
      generatedBy: {
        firstName: (authContext.user as any).firstName,
        lastName: (authContext.user as any).lastName,
        email: (authContext.user as any).email,
      },
    };

    return NextResponse.json({
      success: true,
      ...reportData,
    });

  } catch (error) {
    console.error('Error generating outstanding balances report:', error);
    return NextResponse.json(
      { error: 'Failed to generate outstanding balances report' },
      { status: 500 }
    );
  }
}
