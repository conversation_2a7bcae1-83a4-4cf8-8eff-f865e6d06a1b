import { NextRequest, NextResponse } from 'next/server'
import { authenticateWithPayload, makeAuthenticatedPayloadRequest } from '../../../../lib/payload-auth-middleware'

/**
 * GET /api/reports/financial - Generate financial reports
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check permissions - only admin and front-desk can view financial reports
    if (!['admin', 'front-desk'].includes(authContext.user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view financial reports' },
        { status: 403 }
      );
    }

    const url = new URL(request.url);
    const startDate = url.searchParams.get('startDate');
    const endDate = url.searchParams.get('endDate');
    const reportType = url.searchParams.get('type') || 'summary';

    // Set default date range if not provided (last 30 days)
    const defaultEndDate = new Date();
    const defaultStartDate = new Date();
    defaultStartDate.setDate(defaultStartDate.getDate() - 30);

    const dateFilter = {
      createdAt: {
        greater_than_equal: startDate || defaultStartDate.toISOString(),
        less_than_equal: endDate || defaultEndDate.toISOString(),
      }
    };

    // Get bills data
    const bills = await makeAuthenticatedPayloadRequest(
      authContext,
      'bills',
      'find',
      {
        where: dateFilter,
        limit: 1000,
        depth: 1,
      }
    );

    // Get payments data
    const payments = await makeAuthenticatedPayloadRequest(
      authContext,
      'payments',
      'find',
      {
        where: {
          ...dateFilter,
          paymentStatus: { equals: 'completed' }
        },
        limit: 1000,
        depth: 1,
      }
    );

    // Get deposits data
    const deposits = await makeAuthenticatedPayloadRequest(
      authContext,
      'deposits',
      'find',
      {
        where: dateFilter,
        limit: 1000,
        depth: 1,
      }
    );

    // Calculate summary statistics
    const billsTotal = (bills as any).docs.reduce((sum: number, bill: any) => sum + bill.totalAmount, 0);
    const billsPaid = (bills as any).docs.reduce((sum: number, bill: any) => sum + (bill.paidAmount || 0), 0);
    const billsRemaining = (bills as any).docs.reduce((sum: number, bill: any) => sum + (bill.remainingAmount || 0), 0);

    const paymentsTotal = (payments as any).docs.reduce((sum: number, payment: any) => sum + payment.amount, 0);

    const depositsTotal = (deposits as any).docs.reduce((sum: number, deposit: any) => sum + deposit.amount, 0);
    const depositsUsed = (deposits as any).docs.reduce((sum: number, deposit: any) => sum + (deposit.usedAmount || 0), 0);
    const depositsRemaining = (deposits as any).docs.reduce((sum: number, deposit: any) => sum + (deposit.remainingAmount || 0), 0);

    // Payment method breakdown
    const paymentMethodBreakdown = (payments as any).docs.reduce((acc: any, payment: any) => {
      const method = payment.paymentMethod;
      acc[method] = (acc[method] || 0) + payment.amount;
      return acc;
    }, {});

    // Bill status breakdown
    const billStatusBreakdown = (bills as any).docs.reduce((acc: any, bill: any) => {
      const status = bill.status;
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {});

    // Bill type breakdown
    const billTypeBreakdown = (bills as any).docs.reduce((acc: any, bill: any) => {
      const type = bill.billType;
      acc[type] = (acc[type] || 0) + bill.totalAmount;
      return acc;
    }, {});

    const reportData = {
      period: {
        startDate: startDate || defaultStartDate.toISOString(),
        endDate: endDate || defaultEndDate.toISOString(),
      },
      summary: {
        bills: {
          count: (bills as any).totalDocs,
          totalAmount: billsTotal,
          paidAmount: billsPaid,
          remainingAmount: billsRemaining,
          collectionRate: billsTotal > 0 ? (billsPaid / billsTotal * 100).toFixed(2) : 0,
        },
        payments: {
          count: (payments as any).totalDocs,
          totalAmount: paymentsTotal,
          averagePayment: (payments as any).totalDocs > 0 ? (paymentsTotal / (payments as any).totalDocs).toFixed(2) : 0,
        },
        deposits: {
          count: (deposits as any).totalDocs,
          totalAmount: depositsTotal,
          usedAmount: depositsUsed,
          remainingAmount: depositsRemaining,
          utilizationRate: depositsTotal > 0 ? (depositsUsed / depositsTotal * 100).toFixed(2) : 0,
        },
      },
      breakdowns: {
        paymentMethods: paymentMethodBreakdown,
        billStatuses: billStatusBreakdown,
        billTypes: billTypeBreakdown,
      },
      generatedAt: new Date().toISOString(),
      generatedBy: {
        firstName: (authContext.user as any).firstName,
        lastName: (authContext.user as any).lastName,
        email: (authContext.user as any).email,
      },
    };

    // Return detailed data if requested
    if (reportType === 'detailed') {
      (reportData as any).details = {
        bills: (bills as any).docs,
        payments: (payments as any).docs,
        deposits: (deposits as any).docs,
      };
    }

    return NextResponse.json({
      success: true,
      report: reportData,
    });

  } catch (error) {
    console.error('Error generating financial report:', error);
    return NextResponse.json(
      { error: 'Failed to generate financial report' },
      { status: 500 }
    );
  }
}
