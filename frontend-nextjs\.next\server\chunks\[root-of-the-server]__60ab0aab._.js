module.exports = {

"[project]/.next-internal/server/app/api/payments/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@opentelemetry/api", () => require("@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/node:crypto [external] (node:crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:crypto", () => require("node:crypto"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/validation/billing-schemas.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Comprehensive validation schemas for billing forms
// Provides robust client-side validation with detailed error messages in Chinese
__turbopack_context__.s({
    "billFilterSchema": (()=>billFilterSchema),
    "billFormSchema": (()=>billFormSchema),
    "billItemSchema": (()=>billItemSchema),
    "billStatusUpdateSchema": (()=>billStatusUpdateSchema),
    "formatValidationErrors": (()=>formatValidationErrors),
    "patientFormSchema": (()=>patientFormSchema),
    "paymentFormSchema": (()=>paymentFormSchema),
    "validateBillFilter": (()=>validateBillFilter),
    "validateBillForm": (()=>validateBillForm),
    "validateBillItem": (()=>validateBillItem),
    "validateBillStatusUpdate": (()=>validateBillStatusUpdate),
    "validatePatientForm": (()=>validatePatientForm),
    "validatePaymentForm": (()=>validatePaymentForm)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zod@3.24.2/node_modules/zod/lib/index.mjs [app-route] (ecmascript)");
;
// Common validation patterns
const positiveNumber = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["number"])().min(0, '金额不能为负数');
const requiredString = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["string"])().min(1, '此字段为必填项');
const optionalString = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["string"])();
const phoneRegex = /^1[3-9]\d{9}$/;
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
// Custom validation functions
const validateCurrency = (value)=>{
    if (value < 0) return false;
    // Check for reasonable decimal places (max 2)
    const decimalPlaces = (value.toString().split('.')[1] || '').length;
    return decimalPlaces <= 2;
};
const validateDateNotInPast = (date)=>{
    const inputDate = new Date(date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return inputDate >= today;
};
const validateDateNotTooFarInFuture = (date)=>{
    const inputDate = new Date(date);
    const maxDate = new Date();
    maxDate.setFullYear(maxDate.getFullYear() + 2); // Max 2 years in future
    return inputDate <= maxDate;
};
const billItemSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["object"])({
    itemType: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["enum"])([
        'treatment',
        'consultation',
        'material',
        'service'
    ], {
        required_error: '请选择项目类型',
        invalid_type_error: '无效的项目类型'
    }),
    itemName: requiredString.max(100, '项目名称不能超过100个字符'),
    description: optionalString.max(500, '描述不能超过500个字符').optional(),
    quantity: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["number"])().min(0.01, '数量必须大于0').max(9999, '数量不能超过9999').refine((val)=>{
        const decimalPlaces = (val.toString().split('.')[1] || '').length;
        return decimalPlaces <= 3;
    }, '数量最多支持3位小数'),
    unitPrice: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["number"])().min(0, '单价不能为负数').max(999999.99, '单价不能超过999,999.99').refine(validateCurrency, '单价格式无效，最多支持2位小数'),
    discountRate: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["number"])().min(0, '折扣率不能为负数').max(100, '折扣率不能超过100%').optional()
}).refine((data)=>{
    // Validate that discount rate makes sense
    if (data.discountRate && data.discountRate > 0 && data.unitPrice === 0) {
        return false;
    }
    return true;
}, {
    message: '单价为0时不能设置折扣',
    path: [
        'discountRate'
    ]
});
const billFormSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["object"])({
    patient: requiredString.uuid('请选择有效的患者'),
    appointment: optionalString.uuid('请选择有效的预约').optional().or((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["literal"])('')),
    treatment: optionalString.uuid('请选择有效的治疗项目').optional().or((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["literal"])('')),
    billType: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["enum"])([
        'treatment',
        'consultation',
        'deposit',
        'additional'
    ], {
        required_error: '请选择账单类型',
        invalid_type_error: '无效的账单类型'
    }),
    description: requiredString.min(2, '账单描述至少需要2个字符').max(200, '账单描述不能超过200个字符'),
    notes: optionalString.max(1000, '备注不能超过1000个字符').optional(),
    dueDate: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["string"])().min(1, '请选择到期日期').refine((date)=>{
        try {
            new Date(date);
            return true;
        } catch  {
            return false;
        }
    }, '请输入有效的日期').refine(validateDateNotInPast, '到期日期不能是过去的日期').refine(validateDateNotTooFarInFuture, '到期日期不能超过2年'),
    discountAmount: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["number"])().min(0, '折扣金额不能为负数').max(999999.99, '折扣金额不能超过999,999.99').refine(validateCurrency, '折扣金额格式无效').optional(),
    taxAmount: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["number"])().min(0, '税费金额不能为负数').max(999999.99, '税费金额不能超过999,999.99').refine(validateCurrency, '税费金额格式无效').optional(),
    items: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["array"])(billItemSchema).min(1, '至少需要一个账单项目').max(50, '账单项目不能超过50个')
}).refine((data)=>{
    // Validate that bill has reasonable total
    const itemsTotal = data.items.reduce((sum, item)=>{
        const itemTotal = item.quantity * item.unitPrice;
        const itemDiscount = itemTotal * ((item.discountRate || 0) / 100);
        return sum + (itemTotal - itemDiscount);
    }, 0);
    const discountAmount = data.discountAmount || 0;
    const taxAmount = data.taxAmount || 0;
    const finalTotal = itemsTotal + taxAmount - discountAmount;
    return finalTotal >= 0;
}, {
    message: '账单总金额不能为负数',
    path: [
        'discountAmount'
    ]
}).refine((data)=>{
    // Validate discount doesn't exceed subtotal
    const itemsTotal = data.items.reduce((sum, item)=>{
        const itemTotal = item.quantity * item.unitPrice;
        const itemDiscount = itemTotal * ((item.discountRate || 0) / 100);
        return sum + (itemTotal - itemDiscount);
    }, 0);
    const discountAmount = data.discountAmount || 0;
    return discountAmount <= itemsTotal;
}, {
    message: '折扣金额不能超过项目小计',
    path: [
        'discountAmount'
    ]
});
const paymentFormSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["object"])({
    amount: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["number"])().min(0.01, '支付金额必须大于0').max(999999.99, '支付金额不能超过999,999.99').refine(validateCurrency, '支付金额格式无效，最多支持2位小数'),
    paymentMethod: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["enum"])([
        'cash',
        'card',
        'wechat',
        'alipay',
        'transfer',
        'installment'
    ], {
        required_error: '请选择支付方式',
        invalid_type_error: '无效的支付方式'
    }),
    transactionId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["string"])().max(100, '交易ID不能超过100个字符').optional().or((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["literal"])('')),
    notes: optionalString.max(500, '备注不能超过500个字符').optional()
}).refine((data)=>{
    // Require transaction ID for certain payment methods
    const methodsRequiringTransactionId = [
        'card',
        'wechat',
        'alipay',
        'transfer'
    ];
    if (methodsRequiringTransactionId.includes(data.paymentMethod)) {
        return data.transactionId && data.transactionId.trim().length > 0;
    }
    return true;
}, {
    message: '此支付方式需要提供交易ID',
    path: [
        'transactionId'
    ]
});
const patientFormSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["object"])({
    fullName: requiredString.min(2, '姓名至少需要2个字符').max(50, '姓名不能超过50个字符').regex(/^[\u4e00-\u9fa5a-zA-Z\s]+$/, '姓名只能包含中文、英文和空格'),
    phone: requiredString.regex(phoneRegex, '请输入有效的手机号码'),
    email: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["string"])().email('请输入有效的邮箱地址').max(100, '邮箱地址不能超过100个字符').optional().or((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["literal"])('')),
    medicalNotes: optionalString.max(2000, '医疗备注不能超过2000个字符').optional()
});
const billStatusUpdateSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["object"])({
    status: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["enum"])([
        'draft',
        'sent',
        'confirmed',
        'paid',
        'cancelled'
    ], {
        required_error: '请选择账单状态',
        invalid_type_error: '无效的账单状态'
    }),
    notes: optionalString.max(500, '状态更新备注不能超过500个字符').optional()
});
const billFilterSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["object"])({
    search: optionalString.max(100, '搜索关键词不能超过100个字符').optional(),
    status: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["enum"])([
        'draft',
        'sent',
        'confirmed',
        'paid',
        'cancelled'
    ]).optional(),
    billType: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["enum"])([
        'treatment',
        'consultation',
        'deposit',
        'additional'
    ]).optional(),
    patientId: optionalString.uuid('请选择有效的患者').optional().or((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["literal"])('')),
    dateFrom: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["string"])().optional().refine((date)=>{
        if (!date) return true;
        try {
            new Date(date);
            return true;
        } catch  {
            return false;
        }
    }, '请输入有效的开始日期'),
    dateTo: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["string"])().optional().refine((date)=>{
        if (!date) return true;
        try {
            new Date(date);
            return true;
        } catch  {
            return false;
        }
    }, '请输入有效的结束日期'),
    amountMin: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["number"])().min(0, '最小金额不能为负数').max(999999.99, '最小金额不能超过999,999.99').optional(),
    amountMax: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$2$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["number"])().min(0, '最大金额不能为负数').max(999999.99, '最大金额不能超过999,999.99').optional()
}).refine((data)=>{
    // Validate date range
    if (data.dateFrom && data.dateTo) {
        const fromDate = new Date(data.dateFrom);
        const toDate = new Date(data.dateTo);
        return fromDate <= toDate;
    }
    return true;
}, {
    message: '开始日期不能晚于结束日期',
    path: [
        'dateTo'
    ]
}).refine((data)=>{
    // Validate amount range
    if (data.amountMin !== undefined && data.amountMax !== undefined) {
        return data.amountMin <= data.amountMax;
    }
    return true;
}, {
    message: '最小金额不能大于最大金额',
    path: [
        'amountMax'
    ]
});
const validateBillItem = (data)=>{
    return billItemSchema.safeParse(data);
};
const validateBillForm = (data)=>{
    return billFormSchema.safeParse(data);
};
const validatePaymentForm = (data)=>{
    return paymentFormSchema.safeParse(data);
};
const validatePatientForm = (data)=>{
    return patientFormSchema.safeParse(data);
};
const validateBillStatusUpdate = (data)=>{
    return billStatusUpdateSchema.safeParse(data);
};
const validateBillFilter = (data)=>{
    return billFilterSchema.safeParse(data);
};
const formatValidationErrors = (errors)=>{
    return errors.errors.map((error)=>({
            field: error.path.join('.'),
            message: error.message,
            code: error.code
        }));
};
}}),
"[project]/src/lib/billing-error-handler.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Comprehensive error handling system for billing operations
// Provides consistent error handling, logging, and user feedback
__turbopack_context__.s({
    "BillingErrorHandler": (()=>BillingErrorHandler),
    "billingErrorHandler": (()=>billingErrorHandler),
    "handleAPIError": (()=>handleAPIError),
    "handleNetworkError": (()=>handleNetworkError),
    "handleValidationError": (()=>handleValidationError),
    "retryWithBackoff": (()=>retryWithBackoff),
    "showInfo": (()=>showInfo),
    "showSuccess": (()=>showSuccess),
    "showWarning": (()=>showWarning),
    "withErrorHandling": (()=>withErrorHandling)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/sonner@1.7.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/sonner/dist/index.mjs [app-route] (ecmascript)");
;
// Error code mappings with user-friendly messages
const ERROR_MESSAGES = {
    // Authentication errors
    AUTH_REQUIRED: {
        message: '请先登录以继续操作',
        severity: 'error'
    },
    AUTH_SERVICE_ERROR: {
        message: '认证服务暂时不可用，请稍后重试',
        severity: 'error'
    },
    CLERK_USER_FETCH_ERROR: {
        message: '获取用户信息失败，请重新登录',
        severity: 'error'
    },
    USER_EMAIL_MISSING: {
        message: '用户邮箱信息缺失，请联系管理员',
        severity: 'error'
    },
    // Validation errors
    VALIDATION_ERROR: {
        message: '输入数据验证失败，请检查表单内容',
        severity: 'error'
    },
    INVALID_JSON: {
        message: '数据格式错误，请刷新页面重试',
        severity: 'error'
    },
    INVALID_LIMIT: {
        message: '分页参数错误',
        severity: 'warning'
    },
    INVALID_PAGE: {
        message: '页码参数错误',
        severity: 'warning'
    },
    SUBTOTAL_MISMATCH: {
        message: '账单金额计算错误，请重新检查',
        severity: 'error'
    },
    // Business logic errors
    INVALID_PAYMENT_AMOUNT: {
        message: '支付金额无效',
        severity: 'error'
    },
    BILL_NOT_FOUND: {
        message: '账单不存在或已被删除',
        severity: 'error'
    },
    PAYMENT_METHOD_ERROR: {
        message: '支付方式验证失败',
        severity: 'error'
    },
    INSUFFICIENT_PERMISSIONS: {
        message: '权限不足，无法执行此操作',
        severity: 'error'
    },
    // Backend/Service errors
    BACKEND_ERROR: {
        message: '后端服务错误',
        severity: 'error'
    },
    BACKEND_SERVICE_ERROR: {
        message: '后端服务暂时不可用，请稍后重试',
        severity: 'error'
    },
    DATABASE_ERROR: {
        message: '数据库操作失败，请稍后重试',
        severity: 'error'
    },
    NETWORK_ERROR: {
        message: '网络连接错误，请检查网络连接',
        severity: 'error'
    },
    // Generic errors
    INTERNAL_ERROR: {
        message: '系统内部错误，请稍后重试',
        severity: 'error'
    },
    UNKNOWN_ERROR: {
        message: '发生未知错误，请联系技术支持',
        severity: 'error'
    }
};
class BillingErrorHandler {
    static instance;
    errorLog = [];
    constructor(){}
    static getInstance() {
        if (!BillingErrorHandler.instance) {
            BillingErrorHandler.instance = new BillingErrorHandler();
        }
        return BillingErrorHandler.instance;
    }
    /**
   * Handle API response errors
   */ handleAPIError(error, options = {}) {
        const { showToast = true, logError = true, context = 'API', fallbackMessage = '操作失败，请稍后重试' } = options;
        let billingError;
        if (error?.code && ERROR_MESSAGES[error.code]) {
            const errorInfo = ERROR_MESSAGES[error.code];
            billingError = {
                code: error.code,
                message: error.message || errorInfo.message,
                userMessage: error.message || errorInfo.message,
                severity: errorInfo.severity,
                details: error.details,
                timestamp: new Date(),
                context
            };
        } else {
            // Handle unknown errors
            billingError = {
                code: 'UNKNOWN_ERROR',
                message: error?.message || 'Unknown error occurred',
                userMessage: fallbackMessage,
                severity: 'error',
                details: error,
                timestamp: new Date(),
                context
            };
        }
        if (logError) {
            this.logError(billingError);
        }
        if (showToast) {
            this.showErrorToast(billingError);
        }
        return billingError;
    }
    /**
   * Handle network/fetch errors
   */ handleNetworkError(error, options = {}) {
        const networkError = {
            code: 'NETWORK_ERROR',
            message: error?.message || 'Network request failed',
            userMessage: '网络连接错误，请检查网络连接后重试',
            severity: 'error',
            details: error,
            timestamp: new Date(),
            context: options.context || 'Network'
        };
        if (options.logError !== false) {
            this.logError(networkError);
        }
        if (options.showToast !== false) {
            this.showErrorToast(networkError);
        }
        return networkError;
    }
    /**
   * Handle validation errors
   */ handleValidationError(validationErrors, options = {}) {
        const firstError = validationErrors[0];
        const validationError = {
            code: 'VALIDATION_ERROR',
            message: 'Validation failed',
            userMessage: firstError?.message || '表单验证失败，请检查输入内容',
            severity: 'error',
            details: validationErrors,
            timestamp: new Date(),
            context: options.context || 'Validation'
        };
        if (options.logError !== false) {
            this.logError(validationError);
        }
        if (options.showToast !== false) {
            this.showErrorToast(validationError);
        }
        return validationError;
    }
    /**
   * Show success message
   */ showSuccess(message, description) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].success(message, {
            description,
            duration: 3000
        });
    }
    /**
   * Show warning message
   */ showWarning(message, description) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].warning(message, {
            description,
            duration: 4000
        });
    }
    /**
   * Show info message
   */ showInfo(message, description) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].info(message, {
            description,
            duration: 3000
        });
    }
    /**
   * Log error to console and internal log
   */ logError(error) {
        console.error(`[${error.context}] ${error.code}: ${error.message}`, {
            userMessage: error.userMessage,
            details: error.details,
            timestamp: error.timestamp
        });
        // Add to internal error log (keep last 100 errors)
        this.errorLog.push(error);
        if (this.errorLog.length > 100) {
            this.errorLog.shift();
        }
    }
    /**
   * Show error toast notification
   */ showErrorToast(error) {
        const toastOptions = {
            duration: error.severity === 'error' ? 5000 : 4000
        };
        switch(error.severity){
            case 'error':
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].error(error.userMessage, toastOptions);
                break;
            case 'warning':
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].warning(error.userMessage, toastOptions);
                break;
            case 'info':
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].info(error.userMessage, toastOptions);
                break;
        }
    }
    /**
   * Get error log for debugging
   */ getErrorLog() {
        return [
            ...this.errorLog
        ];
    }
    /**
   * Clear error log
   */ clearErrorLog() {
        this.errorLog = [];
    }
}
const billingErrorHandler = BillingErrorHandler.getInstance();
const handleAPIError = (error, options)=>billingErrorHandler.handleAPIError(error, options);
const handleNetworkError = (error, options)=>billingErrorHandler.handleNetworkError(error, options);
const handleValidationError = (errors, options)=>billingErrorHandler.handleValidationError(errors, options);
const showSuccess = (message, description)=>billingErrorHandler.showSuccess(message, description);
const showWarning = (message, description)=>billingErrorHandler.showWarning(message, description);
const showInfo = (message, description)=>billingErrorHandler.showInfo(message, description);
const withErrorHandling = (fn, context)=>{
    return async (...args)=>{
        try {
            return await fn(...args);
        } catch (error) {
            handleAPIError(error, {
                context
            });
            throw error;
        }
    };
};
const retryWithBackoff = async (fn, maxRetries = 3, baseDelay = 1000, context)=>{
    let lastError;
    for(let attempt = 1; attempt <= maxRetries; attempt++){
        try {
            return await fn();
        } catch (error) {
            lastError = error;
            if (attempt === maxRetries) {
                handleAPIError(error, {
                    context: context || 'Retry',
                    fallbackMessage: `操作失败，已重试${maxRetries}次`
                });
                throw error;
            }
            // Exponential backoff delay
            const delay = baseDelay * Math.pow(2, attempt - 1);
            await new Promise((resolve)=>setTimeout(resolve, delay));
        }
    }
    throw lastError;
};
}}),
"[project]/src/lib/api/billing.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Billing API client functions for medical clinic system
// Handles bills, payments, and financial operations with comprehensive error handling
__turbopack_context__.s({
    "BillingAPIError": (()=>BillingAPIError),
    "billingUtils": (()=>billingUtils),
    "billsAPI": (()=>billsAPI),
    "depositsAPI": (()=>depositsAPI),
    "paymentsAPI": (()=>paymentsAPI),
    "receiptsAPI": (()=>receiptsAPI),
    "reportsAPI": (()=>reportsAPI)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$billing$2d$error$2d$handler$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/billing-error-handler.ts [app-route] (ecmascript)");
;
// API base URL - using relative paths for Next.js API routes
const API_BASE = '/api';
class BillingAPIError extends Error {
    status;
    code;
    details;
    constructor(message, status, code, details){
        super(message), this.status = status, this.code = code, this.details = details;
        this.name = 'BillingAPIError';
    }
}
// Enhanced API request handler with comprehensive error handling and retry logic
async function apiRequest(endpoint, options = {}, retryOptions) {
    const url = `${API_BASE}${endpoint}`;
    const defaultHeaders = {
        'Content-Type': 'application/json'
    };
    const config = {
        ...options,
        headers: {
            ...defaultHeaders,
            ...options.headers
        }
    };
    const makeRequest = async ()=>{
        try {
            const response = await fetch(url, config);
            if (!response.ok) {
                let errorData;
                try {
                    errorData = await response.json();
                } catch  {
                    errorData = {
                        error: `HTTP ${response.status}: ${response.statusText}`,
                        code: `HTTP_${response.status}`,
                        message: response.statusText
                    };
                }
                const apiError = new BillingAPIError(errorData.error || errorData.message || `HTTP ${response.status}: ${response.statusText}`, response.status, errorData.code || `HTTP_${response.status}`, errorData.details);
                // Handle the error through the error handler
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$billing$2d$error$2d$handler$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["handleAPIError"])(errorData, {
                    context: retryOptions?.context || 'API Request',
                    showToast: false // Don't show toast here, let the calling function decide
                });
                throw apiError;
            }
            return await response.json();
        } catch (error) {
            if (error instanceof BillingAPIError) {
                throw error;
            }
            // Handle network errors
            const networkError = new BillingAPIError(error instanceof Error ? error.message : 'Network error occurred', 0, 'NETWORK_ERROR', error);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$billing$2d$error$2d$handler$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["handleNetworkError"])(error, {
                context: retryOptions?.context || 'API Request',
                showToast: false
            });
            throw networkError;
        }
    };
    // Use retry logic for GET requests and other idempotent operations
    const isIdempotent = !options.method || [
        'GET',
        'HEAD',
        'OPTIONS'
    ].includes(options.method.toUpperCase());
    if (isIdempotent && retryOptions?.maxRetries) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$billing$2d$error$2d$handler$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["retryWithBackoff"])(makeRequest, retryOptions.maxRetries, 1000, retryOptions.context);
    }
    return makeRequest();
}
const billsAPI = {
    /**
   * Fetch all bills with optional filtering and pagination
   */ async fetchBills (params) {
        const searchParams = new URLSearchParams();
        if (params?.page) searchParams.append('page', params.page.toString());
        if (params?.limit) searchParams.append('limit', params.limit.toString());
        if (params?.search) searchParams.append('search', params.search);
        if (params?.status) searchParams.append('status', params.status);
        if (params?.patientId) searchParams.append('patient', params.patientId);
        if (params?.dateFrom) searchParams.append('dateFrom', params.dateFrom);
        if (params?.dateTo) searchParams.append('dateTo', params.dateTo);
        const queryString = searchParams.toString();
        const endpoint = `/bills${queryString ? `?${queryString}` : ''}`;
        return apiRequest(endpoint, {}, {
            maxRetries: 3,
            context: 'Fetch Bills'
        });
    },
    /**
   * Fetch a specific bill by ID
   */ async fetchBill (id) {
        return apiRequest(`/bills/${id}`);
    },
    /**
   * Create a new bill
   */ async createBill (billData) {
        return apiRequest('/bills', {
            method: 'POST',
            body: JSON.stringify(billData)
        });
    },
    /**
   * Update an existing bill
   */ async updateBill (id, updateData) {
        return apiRequest(`/bills/${id}`, {
            method: 'PATCH',
            body: JSON.stringify(updateData)
        });
    },
    /**
   * Delete a bill
   */ async deleteBill (id) {
        return apiRequest(`/bills/${id}`, {
            method: 'DELETE'
        });
    },
    /**
   * Generate bill from appointment
   */ async generateFromAppointment (appointmentId, billType = 'treatment') {
        return apiRequest('/bills/generate-from-appointment', {
            method: 'POST',
            body: JSON.stringify({
                appointmentId,
                billType
            })
        });
    },
    /**
   * Check if appointment already has a bill
   */ async checkAppointmentBill (appointmentId) {
        try {
            const response = await billsAPI.fetchBills({
                limit: 1
            });
            const bill = response.docs.find((bill)=>typeof bill.appointment === 'object' && bill.appointment?.id === appointmentId);
            return {
                hasBill: !!bill,
                bill: bill || undefined
            };
        } catch (error) {
            console.error('Failed to check appointment bill:', error);
            return {
                hasBill: false
            };
        }
    }
};
const paymentsAPI = {
    /**
   * Fetch all payments with optional filtering
   */ async fetchPayments (params) {
        const searchParams = new URLSearchParams();
        if (params?.page) searchParams.append('page', params.page.toString());
        if (params?.limit) searchParams.append('limit', params.limit.toString());
        if (params?.billId) searchParams.append('bill', params.billId);
        if (params?.patientId) searchParams.append('patient', params.patientId);
        if (params?.paymentMethod) searchParams.append('paymentMethod', params.paymentMethod);
        if (params?.status) searchParams.append('paymentStatus', params.status);
        if (params?.dateFrom) searchParams.append('dateFrom', params.dateFrom);
        if (params?.dateTo) searchParams.append('dateTo', params.dateTo);
        const queryString = searchParams.toString();
        const endpoint = `/payments${queryString ? `?${queryString}` : ''}`;
        return apiRequest(endpoint);
    },
    /**
   * Fetch a specific payment by ID
   */ async fetchPayment (id) {
        return apiRequest(`/payments/${id}`);
    },
    /**
   * Process a new payment
   */ async processPayment (paymentData) {
        return apiRequest('/payments', {
            method: 'POST',
            body: JSON.stringify(paymentData)
        });
    },
    /**
   * Update payment status
   */ async updatePayment (id, updateData) {
        return apiRequest(`/payments/${id}`, {
            method: 'PATCH',
            body: JSON.stringify(updateData)
        });
    },
    /**
   * Process refund
   */ async processRefund (paymentId, refundData) {
        return apiRequest(`/payments/${paymentId}/refund`, {
            method: 'POST',
            body: JSON.stringify(refundData)
        });
    }
};
const reportsAPI = {
    /**
   * Get daily revenue report
   */ async getDailyRevenue (date) {
        return apiRequest(`/reports/daily-revenue?date=${date}`);
    },
    /**
   * Get monthly revenue report
   */ async getMonthlyRevenue (year, month) {
        return apiRequest(`/reports/monthly-revenue?year=${year}&month=${month}`);
    },
    /**
   * Get outstanding balances report
   */ async getOutstandingBalances () {
        return apiRequest('/reports/outstanding-balances');
    },
    /**
   * Generate financial report
   */ async getFinancialReport (params) {
        const searchParams = new URLSearchParams();
        if (params?.startDate) searchParams.set('startDate', params.startDate);
        if (params?.endDate) searchParams.set('endDate', params.endDate);
        if (params?.type) searchParams.set('type', params.type);
        return apiRequest(`/reports/financial?${searchParams.toString()}`);
    }
};
const depositsAPI = {
    /**
   * Create a new deposit
   */ async createDeposit (depositData) {
        return apiRequest('/deposits', {
            method: 'POST',
            body: JSON.stringify(depositData)
        });
    },
    /**
   * Get deposits with filtering and pagination
   */ async getDeposits (params) {
        const searchParams = new URLSearchParams();
        if (params?.page) searchParams.set('page', params.page.toString());
        if (params?.limit) searchParams.set('limit', params.limit.toString());
        if (params?.patient) searchParams.set('where[patient][equals]', params.patient);
        if (params?.status) searchParams.set('where[status][equals]', params.status);
        if (params?.depositType) searchParams.set('where[depositType][equals]', params.depositType);
        return apiRequest(`/deposits?${searchParams.toString()}`);
    },
    /**
   * Get deposit by ID
   */ async getDepositById (id) {
        return apiRequest(`/deposits/${id}`);
    },
    /**
   * Update deposit
   */ async updateDeposit (id, updateData) {
        return apiRequest(`/deposits/${id}`, {
            method: 'PATCH',
            body: JSON.stringify(updateData)
        });
    },
    /**
   * Apply deposit to bill
   */ async applyToBill (depositId, billId, amount) {
        return apiRequest('/deposits/apply-to-bill', {
            method: 'POST',
            body: JSON.stringify({
                depositId,
                billId,
                amount
            })
        });
    },
    /**
   * Process deposit refund
   */ async processRefund (depositId, refundAmount, refundReason, refundMethod = 'cash') {
        return apiRequest('/deposits/refund', {
            method: 'POST',
            body: JSON.stringify({
                depositId,
                refundAmount,
                refundReason,
                refundMethod
            })
        });
    }
};
const receiptsAPI = {
    /**
   * Generate receipt for payment
   */ async generateReceipt (paymentId) {
        return apiRequest(`/payments/${paymentId}/receipt`);
    },
    /**
   * Regenerate receipt number (admin only)
   */ async regenerateReceipt (paymentId) {
        return apiRequest(`/payments/${paymentId}/receipt`, {
            method: 'POST'
        });
    }
};
;
const billingUtils = {
    /**
   * Format currency amount for display
   */ formatCurrency (amount) {
        return new Intl.NumberFormat('zh-CN', {
            style: 'currency',
            currency: 'CNY'
        }).format(amount);
    },
    /**
   * Calculate bill total with discounts and taxes
   */ calculateBillTotal (subtotal, discountAmount = 0, taxAmount = 0) {
        return subtotal + taxAmount - discountAmount;
    },
    /**
   * Get payment method display name
   */ getPaymentMethodName (method) {
        const methods = {
            cash: '现金',
            card: '银行卡',
            wechat: '微信支付',
            alipay: '支付宝',
            transfer: '银行转账',
            deposit: '押金抵扣',
            installment: '分期付款'
        };
        return methods[method] || method;
    },
    /**
   * Get bill status display name
   */ getBillStatusName (status) {
        const statuses = {
            draft: '草稿',
            sent: '已发送',
            confirmed: '已确认',
            paid: '已支付',
            cancelled: '已取消'
        };
        return statuses[status] || status;
    },
    /**
   * Get payment status display name
   */ getPaymentStatusName (status) {
        const statuses = {
            pending: '待处理',
            completed: '已完成',
            failed: '失败',
            refunded: '已退款'
        };
        return statuses[status] || status;
    },
    /**
   * Get deposit status display name
   */ getDepositStatusName (status) {
        const statuses = {
            active: '有效',
            used: '已使用',
            refunded: '已退还',
            expired: '已过期'
        };
        return statuses[status] || status;
    },
    /**
   * Validate payment amount against bill balance
   */ validatePaymentAmount (amount, billBalance) {
        return amount > 0 && amount <= billBalance;
    }
};
}}),
"[project]/src/lib/billing-notifications.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Comprehensive toast notification utilities for billing actions
// Provides consistent messaging in Chinese for all billing operations
__turbopack_context__.s({
    "billingNotifications": (()=>billingNotifications),
    "dismissAllToasts": (()=>dismissAllToasts),
    "showCustomToast": (()=>showCustomToast)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/sonner@1.7.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/sonner/dist/index.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$billing$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/billing.ts [app-route] (ecmascript)");
;
;
const billingNotifications = {
    // Bill-related notifications
    bill: {
        created: (bill)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].success(`账单创建成功！`, {
                description: `账单编号: ${bill.billNumber}`,
                duration: 4000
            });
        },
        updated: (bill)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].success(`账单更新成功！`, {
                description: `账单编号: ${bill.billNumber}`,
                duration: 4000
            });
        },
        deleted: (billNumber)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].success(`账单删除成功！`, {
                description: `账单编号: ${billNumber}`,
                duration: 4000
            });
        },
        statusUpdated: (bill, oldStatus, newStatus)=>{
            const statusNames = {
                draft: '草稿',
                sent: '已发送',
                confirmed: '已确认',
                paid: '已支付',
                cancelled: '已取消'
            };
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].success(`账单状态已更新！`, {
                description: `${bill.billNumber}: ${statusNames[oldStatus]} → ${statusNames[newStatus]}`,
                duration: 5000
            });
        },
        generateFromAppointment: (bill, appointmentDate)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].success(`从预约生成账单成功！`, {
                description: `预约日期: ${appointmentDate}，账单编号: ${bill.billNumber}`,
                duration: 4000
            });
        },
        loadError: (error)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].error(`加载账单失败`, {
                description: error || '请检查网络连接后重试',
                duration: 5000
            });
        },
        createError: (error)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].error(`创建账单失败`, {
                description: error || '请检查输入信息后重试',
                duration: 5000
            });
        },
        updateError: (error)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].error(`更新账单失败`, {
                description: error || '请稍后重试',
                duration: 5000
            });
        },
        deleteError: (error)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].error(`删除账单失败`, {
                description: error || '请稍后重试',
                duration: 5000
            });
        },
        validationError: (message)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].error(`账单验证失败`, {
                description: message,
                duration: 5000
            });
        }
    },
    // Payment-related notifications
    payment: {
        processed: (payment)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].success(`支付处理成功！`, {
                description: `支付金额: ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$billing$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["billingUtils"].formatCurrency(payment.amount)}，支付编号: ${payment.paymentNumber}`,
                duration: 5000
            });
        },
        receiptGenerated: (payment)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].success(`收据生成成功！`, {
                description: `收据编号: ${payment.receiptNumber || '待生成'}`,
                duration: 4000
            });
        },
        refunded: (payment, refundAmount)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].success(`退款处理成功！`, {
                description: `退款金额: ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$billing$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["billingUtils"].formatCurrency(refundAmount)}，支付编号: ${payment.paymentNumber}`,
                duration: 5000
            });
        },
        statusUpdated: (payment, oldStatus, newStatus)=>{
            const statusNames = {
                pending: '待处理',
                completed: '已完成',
                failed: '失败',
                refunded: '已退款'
            };
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].success(`支付状态已更新！`, {
                description: `${payment.paymentNumber}: ${statusNames[oldStatus]} → ${statusNames[newStatus]}`,
                duration: 4000
            });
        },
        processError: (error)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].error(`支付处理失败`, {
                description: error || '请检查支付信息后重试',
                duration: 5000
            });
        },
        refundError: (error)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].error(`退款处理失败`, {
                description: error || '请联系管理员处理',
                duration: 5000
            });
        },
        validationError: (message)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].error(`支付验证失败`, {
                description: message,
                duration: 5000
            });
        },
        amountExceeded: (maxAmount)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].error(`支付金额超限`, {
                description: `最大支付金额: ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$billing$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["billingUtils"].formatCurrency(maxAmount)}`,
                duration: 5000
            });
        }
    },
    // Receipt-related notifications
    receipt: {
        printed: (receiptNumber)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].success(`收据打印成功！`, {
                description: `收据编号: ${receiptNumber}`,
                duration: 3000
            });
        },
        downloaded: (receiptNumber)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].success(`收据下载成功！`, {
                description: `收据编号: ${receiptNumber}`,
                duration: 3000
            });
        },
        printError: ()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].error(`收据打印失败`, {
                description: '请检查打印机设置',
                duration: 4000
            });
        },
        downloadError: ()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].error(`收据下载失败`, {
                description: '请稍后重试',
                duration: 4000
            });
        },
        notFound: (receiptNumber)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].error(`收据未找到`, {
                description: `收据编号: ${receiptNumber}`,
                duration: 4000
            });
        }
    },
    // General system notifications
    system: {
        loading: (action)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].loading(`${action}中...`, {
                duration: Infinity
            });
        },
        networkError: ()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].error(`网络连接失败`, {
                description: '请检查网络连接后重试',
                duration: 5000
            });
        },
        permissionDenied: (action)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].error(`权限不足`, {
                description: `您没有权限执行: ${action}`,
                duration: 5000
            });
        },
        dataRefreshed: ()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].success(`数据刷新成功`, {
                duration: 2000
            });
        },
        dataRefreshError: ()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].error(`数据刷新失败`, {
                description: '请稍后重试',
                duration: 4000
            });
        },
        operationCancelled: (operation)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].info(`${operation}已取消`, {
                duration: 2000
            });
        },
        featureNotImplemented: (feature)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].info(`${feature}功能开发中...`, {
                description: '敬请期待',
                duration: 3000
            });
        }
    },
    // Financial reporting notifications
    financial: {
        reportGenerated: (reportType, period)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].success(`${reportType}生成成功！`, {
                description: `报表期间: ${period}`,
                duration: 4000
            });
        },
        reportError: (reportType, error)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].error(`${reportType}生成失败`, {
                description: error || '请稍后重试',
                duration: 5000
            });
        },
        dataExported: (format)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].success(`数据导出成功！`, {
                description: `格式: ${format}`,
                duration: 3000
            });
        },
        exportError: (error)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].error(`数据导出失败`, {
                description: error || '请稍后重试',
                duration: 4000
            });
        }
    },
    // Validation and warning notifications
    validation: {
        requiredField: (fieldName)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].error(`字段验证失败`, {
                description: `${fieldName}为必填项`,
                duration: 4000
            });
        },
        invalidFormat: (fieldName, expectedFormat)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].error(`格式验证失败`, {
                description: `${fieldName}格式应为: ${expectedFormat}`,
                duration: 4000
            });
        },
        duplicateEntry: (itemType, identifier)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].error(`重复条目`, {
                description: `${itemType} "${identifier}" 已存在`,
                duration: 4000
            });
        },
        unsavedChanges: ()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].warning(`有未保存的更改`, {
                description: '请保存后再继续',
                duration: 4000
            });
        },
        confirmAction: (action)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].warning(`请确认操作`, {
                description: `即将执行: ${action}`,
                duration: 5000
            });
        }
    }
};
const dismissAllToasts = ()=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"].dismiss();
};
const showCustomToast = (type, title, description, duration = 4000)=>{
    const toastFunction = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toast"][type];
    toastFunction(title, {
        description,
        duration
    });
};
}}),
"[project]/src/lib/validation/validation-utils.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Validation utilities for real-time form validation and error handling
// Provides consistent validation feedback across all billing forms
__turbopack_context__.s({
    "FieldValidator": (()=>FieldValidator),
    "FormValidator": (()=>FormValidator),
    "VALIDATION_CONSTANTS": (()=>VALIDATION_CONSTANTS),
    "ValidationDebouncer": (()=>ValidationDebouncer),
    "displayValidationErrors": (()=>displayValidationErrors),
    "validateBusinessRules": (()=>validateBusinessRules)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$billing$2d$notifications$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/billing-notifications.ts [app-route] (ecmascript)");
;
class ValidationDebouncer {
    timeouts = new Map();
    delay;
    constructor(delay = 500){
        this.delay = delay;
    }
    debounce(key, callback, ...args) {
        // Clear existing timeout for this key
        const existingTimeout = this.timeouts.get(key);
        if (existingTimeout) {
            clearTimeout(existingTimeout);
        }
        // Set new timeout
        const timeout = setTimeout(()=>{
            callback(...args);
            this.timeouts.delete(key);
        }, this.delay);
        this.timeouts.set(key, timeout);
    }
    clear(key) {
        if (key) {
            const timeout = this.timeouts.get(key);
            if (timeout) {
                clearTimeout(timeout);
                this.timeouts.delete(key);
            }
        } else {
            // Clear all timeouts
            this.timeouts.forEach((timeout)=>clearTimeout(timeout));
            this.timeouts.clear();
        }
    }
}
class FieldValidator {
    schema;
    debouncer;
    constructor(schema, debounceDelay = 300){
        this.schema = schema;
        this.debouncer = new ValidationDebouncer(debounceDelay);
    }
    validateField(fieldPath, value, fullData, onValidation) {
        this.debouncer.debounce(fieldPath, this.performFieldValidation.bind(this), fieldPath, value, fullData, onValidation);
    }
    performFieldValidation(fieldPath, value, fullData, onValidation) {
        try {
            // Create a partial object with just this field
            const fieldData = this.setNestedValue({}, fieldPath, value);
            // Merge with existing data
            const testData = {
                ...fullData,
                ...fieldData
            };
            // Validate the full object but focus on this field
            const result = this.schema.safeParse(testData);
            const fieldErrors = result.success ? [] : result.error.errors.filter((error)=>error.path.join('.') === fieldPath).map((error)=>({
                    field: fieldPath,
                    message: error.message,
                    code: error.code,
                    severity: 'error'
                }));
            const warnings = this.generateWarnings(fieldPath, value, fullData);
            const validationResult = {
                isValid: fieldErrors.length === 0,
                errors: fieldErrors,
                warnings
            };
            if (onValidation) {
                onValidation(validationResult);
            }
        } catch (error) {
            console.error('Field validation error:', error);
            if (onValidation) {
                onValidation({
                    isValid: false,
                    errors: [
                        {
                            field: fieldPath,
                            message: '验证过程中发生错误',
                            code: 'VALIDATION_ERROR',
                            severity: 'error'
                        }
                    ],
                    warnings: []
                });
            }
        }
    }
    setNestedValue(obj, path, value) {
        const keys = path.split('.');
        let current = obj;
        for(let i = 0; i < keys.length - 1; i++){
            const key = keys[i];
            if (!(key in current)) {
                current[key] = {};
            }
            current = current[key];
        }
        current[keys[keys.length - 1]] = value;
        return obj;
    }
    generateWarnings(fieldPath, value, fullData) {
        const warnings = [];
        // Generate context-specific warnings
        switch(fieldPath){
            case 'amount':
                if (typeof value === 'number' && value > 10000) {
                    warnings.push({
                        field: fieldPath,
                        message: '金额较大，请确认是否正确',
                        suggestion: '检查金额是否输入正确'
                    });
                }
                break;
            case 'dueDate':
                if (value) {
                    const dueDate = new Date(value);
                    const today = new Date();
                    const daysDiff = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
                    if (daysDiff > 365) {
                        warnings.push({
                            field: fieldPath,
                            message: '到期日期距离现在超过一年',
                            suggestion: '考虑设置更近的到期日期'
                        });
                    } else if (daysDiff < 7) {
                        warnings.push({
                            field: fieldPath,
                            message: '到期日期较近',
                            suggestion: '确保有足够时间处理账单'
                        });
                    }
                }
                break;
            case 'discountAmount':
                if (typeof value === 'number' && value > 0 && fullData.items) {
                    const subtotal = fullData.items.reduce((sum, item)=>{
                        return sum + (item.quantity || 0) * (item.unitPrice || 0);
                    }, 0);
                    if (value > subtotal * 0.5) {
                        warnings.push({
                            field: fieldPath,
                            message: '折扣金额超过小计的50%',
                            suggestion: '确认折扣金额是否正确'
                        });
                    }
                }
                break;
            case 'unitPrice':
                if (typeof value === 'number' && value === 0) {
                    warnings.push({
                        field: fieldPath,
                        message: '单价为0，确认是否为免费项目',
                        suggestion: '如果不是免费项目，请输入正确单价'
                    });
                }
                break;
        }
        return warnings;
    }
    cleanup() {
        this.debouncer.clear();
    }
}
class FormValidator {
    schema;
    fieldValidators = new Map();
    constructor(schema){
        this.schema = schema;
    }
    validateForm(data) {
        try {
            const result = this.schema.safeParse(data);
            if (result.success) {
                return {
                    isValid: true,
                    errors: [],
                    warnings: this.generateFormWarnings(data)
                };
            }
            const errors = result.error.errors.map((error)=>({
                    field: error.path.join('.'),
                    message: error.message,
                    code: error.code,
                    severity: 'error'
                }));
            return {
                isValid: false,
                errors,
                warnings: this.generateFormWarnings(data)
            };
        } catch (error) {
            console.error('Form validation error:', error);
            return {
                isValid: false,
                errors: [
                    {
                        field: 'form',
                        message: '表单验证过程中发生错误',
                        code: 'FORM_VALIDATION_ERROR',
                        severity: 'error'
                    }
                ],
                warnings: []
            };
        }
    }
    generateFormWarnings(data) {
        const warnings = [];
        // Generate form-level warnings
        if (data.items && Array.isArray(data.items)) {
            const totalItems = data.items.length;
            if (totalItems > 20) {
                warnings.push({
                    field: 'items',
                    message: `账单包含${totalItems}个项目，较多`,
                    suggestion: '考虑合并相似项目或分拆为多个账单'
                });
            }
            const totalAmount = data.items.reduce((sum, item)=>{
                return sum + (item.quantity || 0) * (item.unitPrice || 0);
            }, 0);
            if (totalAmount > 50000) {
                warnings.push({
                    field: 'form',
                    message: '账单总金额较大',
                    suggestion: '确认金额计算是否正确'
                });
            }
        }
        return warnings;
    }
    getFieldValidator(fieldPath) {
        if (!this.fieldValidators.has(fieldPath)) {
            this.fieldValidators.set(fieldPath, new FieldValidator(this.schema));
        }
        return this.fieldValidators.get(fieldPath);
    }
    cleanup() {
        this.fieldValidators.forEach((validator)=>validator.cleanup());
        this.fieldValidators.clear();
    }
}
const displayValidationErrors = (errors)=>{
    errors.forEach((error)=>{
        if (error.severity === 'error') {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$billing$2d$notifications$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["billingNotifications"].validation.requiredField(error.field);
        }
    });
};
const validateBusinessRules = {
    // Check if bill can be marked as paid
    canMarkAsPaid: (bill)=>{
        if ((bill.remainingAmount || 0) > 0) {
            return {
                valid: false,
                reason: '账单还有未支付金额，无法标记为已支付'
            };
        }
        return {
            valid: true
        };
    },
    // Check if payment amount is valid for bill
    isValidPaymentAmount: (amount, bill)=>{
        const remainingAmount = bill.remainingAmount || 0;
        if (amount > remainingAmount) {
            return {
                valid: false,
                reason: `支付金额不能超过待付金额 ¥${remainingAmount.toFixed(2)}`
            };
        }
        if (amount <= 0) {
            return {
                valid: false,
                reason: '支付金额必须大于0'
            };
        }
        return {
            valid: true
        };
    },
    // Check if bill can be deleted
    canDeleteBill: (bill)=>{
        if (bill.status === 'paid') {
            return {
                valid: false,
                reason: '已支付的账单不能删除'
            };
        }
        if ((bill.paidAmount || 0) > 0) {
            return {
                valid: false,
                reason: '已有支付记录的账单不能删除'
            };
        }
        return {
            valid: true
        };
    },
    // Check if bill status transition is valid
    isValidStatusTransition: (fromStatus, toStatus)=>{
        const validTransitions = {
            draft: [
                'sent',
                'cancelled'
            ],
            sent: [
                'confirmed',
                'cancelled'
            ],
            confirmed: [
                'paid',
                'cancelled'
            ],
            paid: [],
            cancelled: []
        };
        const allowedTransitions = validTransitions[fromStatus] || [];
        if (!allowedTransitions.includes(toStatus)) {
            return {
                valid: false,
                reason: `不能从"${fromStatus}"状态转换到"${toStatus}"状态`
            };
        }
        return {
            valid: true
        };
    }
};
const VALIDATION_CONSTANTS = {
    MAX_BILL_AMOUNT: 999999.99,
    MAX_ITEMS_PER_BILL: 50,
    MAX_DESCRIPTION_LENGTH: 200,
    MAX_NOTES_LENGTH: 1000,
    MIN_PAYMENT_AMOUNT: 0.01,
    MAX_DISCOUNT_RATE: 100,
    DEBOUNCE_DELAY: 300
};
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[project]/src/lib/billing-security.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Comprehensive security measures for billing system
// Handles data encryption, audit logging, and secure payment processing
__turbopack_context__.s({
    "AuditLogger": (()=>AuditLogger),
    "DataEncryption": (()=>DataEncryption),
    "InputSanitizer": (()=>InputSanitizer),
    "RateLimiter": (()=>RateLimiter),
    "auditLogger": (()=>auditLogger),
    "cleanupSecurity": (()=>cleanupSecurity),
    "rateLimiter": (()=>rateLimiter)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/crypto [external] (crypto, cjs)");
;
// Security configuration
const SECURITY_CONFIG = {
    encryption: {
        algorithm: 'aes-256-gcm',
        keyLength: 32,
        ivLength: 16,
        tagLength: 16
    },
    audit: {
        maxLogSize: 10000,
        sensitiveFields: [
            'cardNumber',
            'cvv',
            'bankAccount',
            'transactionId'
        ]
    },
    rateLimit: {
        maxRequestsPerMinute: 60,
        maxPaymentRequestsPerHour: 10
    }
};
class DataEncryption {
    static getEncryptionKey() {
        const key = process.env.BILLING_ENCRYPTION_KEY;
        if (!key) {
            throw new Error('BILLING_ENCRYPTION_KEY environment variable is required');
        }
        return key;
    }
    /**
   * Encrypt sensitive data
   */ static encrypt(data) {
        try {
            const key = Buffer.from(this.getEncryptionKey(), 'hex');
            const iv = __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].randomBytes(SECURITY_CONFIG.encryption.ivLength);
            const cipher = __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].createCipher(SECURITY_CONFIG.encryption.algorithm, key);
            cipher.setAAD(Buffer.from('billing-data'));
            let encrypted = cipher.update(data, 'utf8', 'hex');
            encrypted += cipher.final('hex');
            const tag = cipher.getAuthTag();
            return {
                encrypted,
                iv: iv.toString('hex'),
                tag: tag.toString('hex')
            };
        } catch (error) {
            console.error('Encryption error:', error);
            throw new Error('Failed to encrypt sensitive data');
        }
    }
    /**
   * Decrypt sensitive data
   */ static decrypt(encryptedData) {
        try {
            const key = Buffer.from(this.getEncryptionKey(), 'hex');
            const iv = Buffer.from(encryptedData.iv, 'hex');
            const tag = Buffer.from(encryptedData.tag, 'hex');
            const decipher = __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].createDecipher(SECURITY_CONFIG.encryption.algorithm, key);
            decipher.setAAD(Buffer.from('billing-data'));
            decipher.setAuthTag(tag);
            let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
            decrypted += decipher.final('utf8');
            return decrypted;
        } catch (error) {
            console.error('Decryption error:', error);
            throw new Error('Failed to decrypt sensitive data');
        }
    }
    /**
   * Hash sensitive data for comparison (one-way)
   */ static hash(data) {
        return __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].createHash('sha256').update(data).digest('hex');
    }
    /**
   * Generate secure random token
   */ static generateSecureToken(length = 32) {
        return __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].randomBytes(length).toString('hex');
    }
}
class AuditLogger {
    static instance;
    auditLog = [];
    constructor(){}
    static getInstance() {
        if (!AuditLogger.instance) {
            AuditLogger.instance = new AuditLogger();
        }
        return AuditLogger.instance;
    }
    /**
   * Log financial operation
   */ logFinancialOperation(userId, userEmail, action, resource, details, success = true, errorMessage, request) {
        const entry = {
            id: DataEncryption.generateSecureToken(16),
            timestamp: new Date(),
            userId,
            userEmail,
            action,
            resource,
            resourceId: details.id || details.billId || details.paymentId,
            details: this.sanitizeDetails(details),
            ipAddress: this.getClientIP(request),
            userAgent: request?.headers.get('user-agent') || undefined,
            success,
            errorMessage
        };
        this.auditLog.push(entry);
        // Keep only the most recent entries
        if (this.auditLog.length > SECURITY_CONFIG.audit.maxLogSize) {
            this.auditLog.shift();
        }
        // Log to console for development (in production, this should go to a secure logging service)
        console.log(`[AUDIT] ${entry.timestamp.toISOString()} - ${entry.action} on ${entry.resource} by ${entry.userEmail} - ${entry.success ? 'SUCCESS' : 'FAILED'}`);
    }
    /**
   * Get audit log entries (filtered for security)
   */ getAuditLog(userId, limit = 100) {
        let filteredLog = this.auditLog;
        if (userId) {
            filteredLog = filteredLog.filter((entry)=>entry.userId === userId);
        }
        return filteredLog.slice(-limit).map((entry)=>({
                ...entry,
                details: this.sanitizeDetails(entry.details)
            }));
    }
    /**
   * Remove sensitive information from audit details
   */ sanitizeDetails(details) {
        if (!details || typeof details !== 'object') {
            return details;
        }
        const sanitized = {
            ...details
        };
        SECURITY_CONFIG.audit.sensitiveFields.forEach((field)=>{
            if (sanitized[field]) {
                sanitized[field] = this.maskSensitiveData(sanitized[field]);
            }
        });
        return sanitized;
    }
    /**
   * Mask sensitive data for logging
   */ maskSensitiveData(data) {
        if (data.length <= 4) {
            return '****';
        }
        return data.substring(0, 2) + '*'.repeat(data.length - 4) + data.substring(data.length - 2);
    }
    /**
   * Extract client IP address from request
   */ getClientIP(request) {
        if (!request) return undefined;
        const forwarded = request.headers.get('x-forwarded-for');
        if (forwarded) {
            return forwarded.split(',')[0].trim();
        }
        const realIP = request.headers.get('x-real-ip');
        if (realIP) {
            return realIP;
        }
        return 'unknown';
    }
}
class RateLimiter {
    static instance;
    requestCounts = new Map();
    paymentCounts = new Map();
    constructor(){}
    static getInstance() {
        if (!RateLimiter.instance) {
            RateLimiter.instance = new RateLimiter();
        }
        return RateLimiter.instance;
    }
    /**
   * Check if request is within rate limit
   */ checkRateLimit(userId, isPaymentRequest = false) {
        const now = Date.now();
        const limits = isPaymentRequest ? {
            max: SECURITY_CONFIG.rateLimit.maxPaymentRequestsPerHour,
            window: 60 * 60 * 1000
        } : {
            max: SECURITY_CONFIG.rateLimit.maxRequestsPerMinute,
            window: 60 * 1000
        };
        const counts = isPaymentRequest ? this.paymentCounts : this.requestCounts;
        const userCount = counts.get(userId);
        if (!userCount || now > userCount.resetTime) {
            // Reset or initialize counter
            counts.set(userId, {
                count: 1,
                resetTime: now + limits.window
            });
            return {
                allowed: true
            };
        }
        if (userCount.count >= limits.max) {
            return {
                allowed: false,
                resetTime: userCount.resetTime
            };
        }
        userCount.count++;
        return {
            allowed: true
        };
    }
    /**
   * Clear expired rate limit entries
   */ cleanup() {
        const now = Date.now();
        for (const [userId, data] of this.requestCounts.entries()){
            if (now > data.resetTime) {
                this.requestCounts.delete(userId);
            }
        }
        for (const [userId, data] of this.paymentCounts.entries()){
            if (now > data.resetTime) {
                this.paymentCounts.delete(userId);
            }
        }
    }
}
class InputSanitizer {
    /**
   * Sanitize and validate monetary amounts
   */ static sanitizeAmount(amount) {
        if (typeof amount === 'number') {
            if (!isFinite(amount) || amount < 0) {
                throw new Error('Invalid amount: must be a positive finite number');
            }
            return Math.round(amount * 100) / 100; // Round to 2 decimal places
        }
        if (typeof amount === 'string') {
            const parsed = parseFloat(amount.replace(/[^\d.-]/g, ''));
            if (isNaN(parsed) || parsed < 0) {
                throw new Error('Invalid amount: must be a positive number');
            }
            return Math.round(parsed * 100) / 100;
        }
        throw new Error('Invalid amount: must be a number or numeric string');
    }
    /**
   * Sanitize text input to prevent XSS and injection attacks
   */ static sanitizeText(text, maxLength = 1000) {
        if (typeof text !== 'string') {
            throw new Error('Input must be a string');
        }
        // Remove potentially dangerous characters and HTML tags
        const sanitized = text.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '').replace(/<[^>]*>/g, '').replace(/javascript:/gi, '').replace(/on\w+\s*=/gi, '').trim();
        if (sanitized.length > maxLength) {
            throw new Error(`Input too long: maximum ${maxLength} characters allowed`);
        }
        return sanitized;
    }
    /**
   * Validate and sanitize payment method
   */ static sanitizePaymentMethod(method) {
        const validMethods = [
            'cash',
            'card',
            'wechat',
            'alipay',
            'transfer',
            'installment'
        ];
        if (!validMethods.includes(method)) {
            throw new Error('Invalid payment method');
        }
        return method;
    }
    /**
   * Sanitize transaction ID
   */ static sanitizeTransactionId(transactionId) {
        if (typeof transactionId !== 'string') {
            throw new Error('Transaction ID must be a string');
        }
        // Allow only alphanumeric characters, hyphens, and underscores
        const sanitized = transactionId.replace(/[^a-zA-Z0-9\-_]/g, '');
        if (sanitized.length < 3 || sanitized.length > 100) {
            throw new Error('Transaction ID must be between 3 and 100 characters');
        }
        return sanitized;
    }
}
const auditLogger = AuditLogger.getInstance();
const rateLimiter = RateLimiter.getInstance();
const cleanupSecurity = ()=>{
    rateLimiter.cleanup();
};
}}),
"[project]/src/app/api/payments/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$clerk$2b$nextjs$40$6$2e$12$2e$12_next$40$_f2abd70610af0db25eb1f4c2a7e1d3bb$2f$node_modules$2f40$clerk$2f$nextjs$2f$dist$2f$esm$2f$app$2d$router$2f$server$2f$auth$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/app-router/server/auth.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$billing$2d$schemas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/billing-schemas.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$validation$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/validation/validation-utils.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$billing$2d$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/billing-security.ts [app-route] (ecmascript)");
;
;
;
;
;
;
const BACKEND_URL = ("TURBOPACK compile-time value", "http://localhost:8002") || 'http://localhost:8002';
// Enhanced error handling utility
class APIError extends Error {
    status;
    code;
    details;
    constructor(message, status = 500, code, details){
        super(message), this.status = status, this.code = code, this.details = details;
        this.name = 'APIError';
    }
}
// Utility to get user info from Clerk with error handling
async function getClerkUserInfo(userId) {
    try {
        const response = await fetch(`https://api.clerk.com/v1/users/${userId}`, {
            headers: {
                Authorization: `Bearer ${process.env.CLERK_SECRET_KEY}`
            }
        });
        if (!response.ok) {
            throw new APIError('Failed to fetch user information', 401, 'CLERK_USER_FETCH_ERROR');
        }
        return await response.json();
    } catch (error) {
        console.error('Error fetching Clerk user:', error);
        throw new APIError('Authentication service unavailable', 503, 'AUTH_SERVICE_ERROR');
    }
}
// Utility to make backend requests with comprehensive error handling
async function makeBackendRequest(url, options, userId, userEmail) {
    try {
        const response = await fetch(url, {
            ...options,
            headers: {
                'Content-Type': 'application/json',
                'x-clerk-user-id': userId,
                'x-user-email': userEmail,
                ...options.headers
            }
        });
        const data = await response.json();
        if (!response.ok) {
            throw new APIError(data.error || `Backend request failed: ${response.status}`, response.status, data.code || 'BACKEND_ERROR', data);
        }
        return data;
    } catch (error) {
        if (error instanceof APIError) {
            throw error;
        }
        console.error('Backend request error:', error);
        throw new APIError('Backend service unavailable', 503, 'BACKEND_SERVICE_ERROR');
    }
}
async function GET(request) {
    try {
        const { userId } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$clerk$2b$nextjs$40$6$2e$12$2e$12_next$40$_f2abd70610af0db25eb1f4c2a7e1d3bb$2f$node_modules$2f40$clerk$2f$nextjs$2f$dist$2f$esm$2f$app$2d$router$2f$server$2f$auth$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["auth"])();
        if (!userId) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Authentication required'
            }, {
                status: 401
            });
        }
        // Get user info from Clerk
        const user = await fetch(`https://api.clerk.com/v1/users/${userId}`, {
            headers: {
                Authorization: `Bearer ${process.env.CLERK_SECRET_KEY}`
            }
        }).then((res)=>res.json());
        // Forward request to backend with authentication headers
        const url = new URL(request.url);
        const backendUrl = `${BACKEND_URL}/api/payments${url.search}`;
        const response = await fetch(backendUrl, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'x-clerk-user-id': userId,
                'x-user-email': user.email_addresses[0]?.email_address || ''
            }
        });
        const data = await response.json();
        if (!response.ok) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(data, {
                status: response.status
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(data);
    } catch (error) {
        console.error('Error proxying payments request:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const { userId } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$clerk$2b$nextjs$40$6$2e$12$2e$12_next$40$_f2abd70610af0db25eb1f4c2a7e1d3bb$2f$node_modules$2f40$clerk$2f$nextjs$2f$dist$2f$esm$2f$app$2d$router$2f$server$2f$auth$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["auth"])();
        if (!userId) {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$billing$2d$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["auditLogger"].logFinancialOperation('anonymous', 'anonymous', 'CREATE_PAYMENT_UNAUTHORIZED', 'payments', {
                endpoint: '/api/payments'
            }, false, 'Authentication required', request);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Authentication required',
                code: 'AUTH_REQUIRED',
                message: '请先登录以处理支付'
            }, {
                status: 401
            });
        }
        // Check rate limiting for payment requests (more restrictive)
        const rateLimitResult = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$billing$2d$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["rateLimiter"].checkRateLimit(userId, true);
        if (!rateLimitResult.allowed) {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$billing$2d$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["auditLogger"].logFinancialOperation(userId, 'unknown', 'CREATE_PAYMENT_RATE_LIMITED', 'payments', {
                endpoint: '/api/payments',
                resetTime: rateLimitResult.resetTime
            }, false, 'Payment rate limit exceeded', request);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Payment rate limit exceeded',
                code: 'PAYMENT_RATE_LIMIT_EXCEEDED',
                message: '支付请求过于频繁，请稍后重试',
                resetTime: rateLimitResult.resetTime
            }, {
                status: 429
            });
        }
        // Parse and validate request body
        let body;
        try {
            body = await request.json();
        } catch (error) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Invalid JSON in request body',
                code: 'INVALID_JSON',
                message: '请求数据格式错误'
            }, {
                status: 400
            });
        }
        // Sanitize payment input data
        try {
            body.amount = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$billing$2d$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["InputSanitizer"].sanitizeAmount(body.amount);
            body.paymentMethod = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$billing$2d$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["InputSanitizer"].sanitizePaymentMethod(body.paymentMethod);
            if (body.transactionId) {
                body.transactionId = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$billing$2d$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["InputSanitizer"].sanitizeText(body.transactionId, 100);
            }
            if (body.notes) {
                body.notes = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$billing$2d$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["InputSanitizer"].sanitizeText(body.notes, 500);
            }
        } catch (sanitizationError) {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$billing$2d$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["auditLogger"].logFinancialOperation(userId, userEmail, 'CREATE_PAYMENT_SANITIZATION_FAILED', 'payments', {
                error: sanitizationError
            }, false, sanitizationError instanceof Error ? sanitizationError.message : 'Payment sanitization failed', request);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Input sanitization failed',
                code: 'SANITIZATION_ERROR',
                message: '支付数据格式不正确'
            }, {
                status: 400
            });
        }
        // Validate payment data using Zod schema
        const validationResult = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$billing$2d$schemas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["paymentFormSchema"].safeParse(body);
        if (!validationResult.success) {
            const formattedErrors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$billing$2d$schemas$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["formatValidationErrors"])(validationResult.error);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$billing$2d$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["auditLogger"].logFinancialOperation(userId, userEmail, 'CREATE_PAYMENT_VALIDATION_FAILED', 'payments', {
                validationErrors: formattedErrors
            }, false, 'Payment validation failed', request);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Validation failed',
                code: 'VALIDATION_ERROR',
                message: '支付数据验证失败',
                details: formattedErrors
            }, {
                status: 400
            });
        }
        const validatedData = validationResult.data;
        // Get user info from Clerk with error handling
        const user = await getClerkUserInfo(userId);
        const userEmail = user.email_addresses[0]?.email_address || '';
        if (!userEmail) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'User email not found',
                code: 'USER_EMAIL_MISSING',
                message: '用户邮箱信息缺失，请联系管理员'
            }, {
                status: 400
            });
        }
        // If bill ID is provided, validate payment amount against bill
        if (body.billId) {
            try {
                // Fetch bill details to validate payment amount
                const billUrl = `${BACKEND_URL}/api/bills/${body.billId}`;
                const bill = await makeBackendRequest(billUrl, {
                    method: 'GET'
                }, userId, userEmail);
                // Validate payment amount against remaining bill amount
                const paymentValidation = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$validation$2f$validation$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateBusinessRules"].isValidPaymentAmount(validatedData.amount, bill);
                if (!paymentValidation.valid) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                        error: 'Invalid payment amount',
                        code: 'INVALID_PAYMENT_AMOUNT',
                        message: paymentValidation.reason
                    }, {
                        status: 400
                    });
                }
            } catch (error) {
                if (error instanceof APIError && error.status === 404) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                        error: 'Bill not found',
                        code: 'BILL_NOT_FOUND',
                        message: '指定的账单不存在'
                    }, {
                        status: 404
                    });
                }
                throw error; // Re-throw other errors
            }
        }
        // Additional business logic validation for payment methods
        const paymentMethodValidation = validatePaymentMethod(validatedData);
        if (!paymentMethodValidation.valid) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Payment method validation failed',
                code: 'PAYMENT_METHOD_ERROR',
                message: paymentMethodValidation.reason
            }, {
                status: 400
            });
        }
        // Forward request to backend with authentication headers
        const backendUrl = `${BACKEND_URL}/api/payments`;
        const data = await makeBackendRequest(backendUrl, {
            method: 'POST',
            body: JSON.stringify(validatedData)
        }, userId, userEmail);
        // Log successful payment creation (mask sensitive data)
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$billing$2d$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["auditLogger"].logFinancialOperation(userId, userEmail, 'CREATE_PAYMENT', 'payments', {
            paymentId: data.id,
            billId: validatedData.billId,
            amount: validatedData.amount,
            paymentMethod: validatedData.paymentMethod,
            transactionId: validatedData.transactionId,
            patientId: validatedData.patientId
        }, true, undefined, request);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(data, {
            status: 201
        });
    } catch (error) {
        if (error instanceof APIError) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: error.message,
                code: error.code,
                message: error.message,
                details: error.details
            }, {
                status: error.status
            });
        }
        console.error('Unexpected error in POST /api/payments:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$2_$40$babel$2b$core$40$7$2e$2_779ed0850b03e649bfcf2a817b1ecc4b$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error',
            code: 'INTERNAL_ERROR',
            message: '处理支付时发生服务器错误，请稍后重试'
        }, {
            status: 500
        });
    }
}
// Additional payment method validation
function validatePaymentMethod(paymentData) {
    const { paymentMethod, transactionId, amount } = paymentData;
    // Validate transaction ID requirements for different payment methods
    const methodsRequiringTransactionId = [
        'card',
        'wechat',
        'alipay',
        'transfer'
    ];
    if (methodsRequiringTransactionId.includes(paymentMethod)) {
        if (!transactionId || transactionId.trim().length === 0) {
            return {
                valid: false,
                reason: `${getPaymentMethodName(paymentMethod)}需要提供交易ID`
            };
        }
    }
    // Validate amount limits for different payment methods
    if (paymentMethod === 'cash' && amount > 50000) {
        return {
            valid: false,
            reason: '现金支付单笔金额不能超过50,000元'
        };
    }
    if (paymentMethod === 'wechat' && amount > 200000) {
        return {
            valid: false,
            reason: '微信支付单笔金额不能超过200,000元'
        };
    }
    if (paymentMethod === 'alipay' && amount > 200000) {
        return {
            valid: false,
            reason: '支付宝单笔金额不能超过200,000元'
        };
    }
    return {
        valid: true
    };
}
// Helper function to get payment method display name
function getPaymentMethodName(method) {
    const methods = {
        cash: '现金',
        card: '银行卡',
        wechat: '微信支付',
        alipay: '支付宝',
        transfer: '银行转账',
        installment: '分期付款'
    };
    return methods[method] || method;
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__60ab0aab._.js.map