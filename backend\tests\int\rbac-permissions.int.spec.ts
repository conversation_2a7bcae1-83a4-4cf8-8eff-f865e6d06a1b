import { getPayload, Payload } from 'payload'
import config from '@/payload.config'
import { describe, it, beforeAll, afterAll, beforeEach, expect } from 'vitest'

let payload: Payload
let adminUser: any
let doctorUser: any
let frontDeskUser: any
let testPatient: any
let testBill: any
let testPayment: any
let testDeposit: any

describe('RBAC Permissions Integration Tests', () => {
  beforeAll(async () => {
    const payloadConfig = await config
    payload = await getPayload({ config: payloadConfig })

    // Create test users with different roles
    adminUser = await payload.create({
      collection: 'users',
      data: {
        email: `admin-${Date.now()}@clinic.com`,
        role: 'admin',
        firstName: 'Admin',
        lastName: 'User',
        clerkId: `clerk_admin_${Date.now()}`,
      },
    })

    doctorUser = await payload.create({
      collection: 'users',
      data: {
        email: `doctor-${Date.now()}@clinic.com`,
        role: 'doctor',
        firstName: 'Doctor',
        lastName: 'User',
        clerkId: `clerk_doctor_${Date.now()}`,
      },
    })

    frontDeskUser = await payload.create({
      collection: 'users',
      data: {
        email: `frontdesk-${Date.now()}@clinic.com`,
        role: 'front-desk',
        firstName: 'FrontDesk',
        lastName: 'User',
        clerkId: `clerk_frontdesk_${Date.now()}`,
      },
    })

    // Create test patient
    testPatient = await payload.create({
      collection: 'patients',
      data: {
        fullName: 'RBAC测试患者',
        phone: `139${Date.now().toString().slice(-8)}`,
        email: `rbac-patient-${Date.now()}@test.com`,
        medicalNotes: 'RBAC权限测试',
      },
    })

    // Create test bill
    testBill = await payload.create({
      collection: 'bills',
      data: {
        patient: testPatient.id,
        billType: 'treatment',
        subtotal: 1000,
        totalAmount: 1000,
        status: 'confirmed',
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        createdBy: adminUser.id,
      },
    })
  })

  afterAll(async () => {
    // Clean up test data
    if (testPayment) await payload.delete({ collection: 'payments', id: testPayment.id }).catch(() => {})
    if (testDeposit) await payload.delete({ collection: 'deposits', id: testDeposit.id }).catch(() => {})
    if (testBill) await payload.delete({ collection: 'bills', id: testBill.id }).catch(() => {})
    if (testPatient) await payload.delete({ collection: 'patients', id: testPatient.id }).catch(() => {})
    if (frontDeskUser) await payload.delete({ collection: 'users', id: frontDeskUser.id }).catch(() => {})
    if (doctorUser) await payload.delete({ collection: 'users', id: doctorUser.id }).catch(() => {})
    if (adminUser) await payload.delete({ collection: 'users', id: adminUser.id }).catch(() => {})
  })

  describe('Bills Collection RBAC', () => {
    it('Admin should have full access to bills', async () => {
      // Admin can create bills
      const bill = await payload.create({
        collection: 'bills',
        data: {
          patient: testPatient.id,
          billType: 'consultation',
          subtotal: 500,
          totalAmount: 500,
          status: 'draft',
          createdBy: adminUser.id,
        },
        user: adminUser,
      })
      expect(bill.id).toBeDefined()

      // Admin can read bills
      const readBill = await payload.findByID({
        collection: 'bills',
        id: bill.id,
        user: adminUser,
      })
      expect(readBill.id).toBe(bill.id)

      // Admin can update bills
      const updatedBill = await payload.update({
        collection: 'bills',
        id: bill.id,
        data: { status: 'sent' },
        user: adminUser,
      })
      expect(updatedBill.status).toBe('sent')

      // Admin can delete bills
      await payload.delete({
        collection: 'bills',
        id: bill.id,
        user: adminUser,
      })
    })

    it('Doctor should have read-only access to bills', async () => {
      // Doctor can read bills
      const readBill = await payload.findByID({
        collection: 'bills',
        id: testBill.id,
        user: doctorUser,
      })
      expect(readBill.id).toBe(testBill.id)

      // Doctor cannot create bills
      try {
        await payload.create({
          collection: 'bills',
          data: {
            patient: testPatient.id,
            billType: 'consultation',
            subtotal: 500,
            totalAmount: 500,
            status: 'draft',
            createdBy: doctorUser.id,
          },
          user: doctorUser,
        })
        expect(true).toBe(false) // Should not reach here
      } catch (error) {
        expect(error).toBeDefined()
      }

      // Doctor cannot update bills
      try {
        await payload.update({
          collection: 'bills',
          id: testBill.id,
          data: { status: 'sent' },
          user: doctorUser,
        })
        expect(true).toBe(false) // Should not reach here
      } catch (error) {
        expect(error).toBeDefined()
      }

      // Doctor cannot delete bills
      try {
        await payload.delete({
          collection: 'bills',
          id: testBill.id,
          user: doctorUser,
        })
        expect(true).toBe(false) // Should not reach here
      } catch (error) {
        expect(error).toBeDefined()
      }
    })

    it('Front-desk should have create/update access but not delete', async () => {
      // Front-desk can create bills
      const bill = await payload.create({
        collection: 'bills',
        data: {
          patient: testPatient.id,
          billType: 'consultation',
          subtotal: 300,
          totalAmount: 300,
          status: 'draft',
          createdBy: frontDeskUser.id,
        },
        user: frontDeskUser,
      })
      expect(bill.id).toBeDefined()

      // Front-desk can update bills
      const updatedBill = await payload.update({
        collection: 'bills',
        id: bill.id,
        data: { status: 'sent' },
        user: frontDeskUser,
      })
      expect(updatedBill.status).toBe('sent')

      // Front-desk cannot delete bills
      try {
        await payload.delete({
          collection: 'bills',
          id: bill.id,
          user: frontDeskUser,
        })
        expect(true).toBe(false) // Should not reach here
      } catch (error) {
        expect(error).toBeDefined()
      }

      // Clean up
      await payload.delete({
        collection: 'bills',
        id: bill.id,
        user: adminUser,
      })
    })
  })

  describe('Payments Collection RBAC', () => {
    it('Admin should have full access to payments', async () => {
      // Admin can create payments
      const payment = await payload.create({
        collection: 'payments',
        data: {
          bill: testBill.id,
          patient: testPatient.id,
          amount: 250.00,
          paymentMethod: 'cash',
          paymentStatus: 'completed',
          paymentDate: new Date(),
          receivedBy: adminUser.id,
          notes: 'Admin权限测试',
        },
        user: adminUser,
      })
      expect(payment.id).toBeDefined()
      testPayment = payment

      // Admin can delete payments
      await payload.delete({
        collection: 'payments',
        id: payment.id,
        user: adminUser,
      })
      testPayment = null
    })

    it('Doctor should have read-only access to payments', async () => {
      // Create payment as admin first
      const payment = await payload.create({
        collection: 'payments',
        data: {
          bill: testBill.id,
          patient: testPatient.id,
          amount: 150.00,
          paymentMethod: 'wechat',
          paymentStatus: 'completed',
          paymentDate: new Date(),
          receivedBy: adminUser.id,
          notes: 'Doctor权限测试',
        },
        user: adminUser,
      })

      // Doctor can read payments
      const readPayment = await payload.findByID({
        collection: 'payments',
        id: payment.id,
        user: doctorUser,
      })
      expect(readPayment.id).toBe(payment.id)

      // Doctor cannot create payments
      try {
        await payload.create({
          collection: 'payments',
          data: {
            bill: testBill.id,
            patient: testPatient.id,
            amount: 100.00,
            paymentMethod: 'cash',
            paymentStatus: 'completed',
            paymentDate: new Date(),
            receivedBy: doctorUser.id,
          },
          user: doctorUser,
        })
        expect(true).toBe(false) // Should not reach here
      } catch (error) {
        expect(error).toBeDefined()
      }

      // Clean up
      await payload.delete({
        collection: 'payments',
        id: payment.id,
        user: adminUser,
      })
    })

    it('Front-desk should have create/update access but not delete', async () => {
      // Front-desk can create payments
      const payment = await payload.create({
        collection: 'payments',
        data: {
          bill: testBill.id,
          patient: testPatient.id,
          amount: 200.00,
          paymentMethod: 'card',
          paymentStatus: 'completed',
          paymentDate: new Date(),
          receivedBy: frontDeskUser.id,
          notes: 'Front-desk权限测试',
        },
        user: frontDeskUser,
      })
      expect(payment.id).toBeDefined()

      // Front-desk cannot delete payments
      try {
        await payload.delete({
          collection: 'payments',
          id: payment.id,
          user: frontDeskUser,
        })
        expect(true).toBe(false) // Should not reach here
      } catch (error) {
        expect(error).toBeDefined()
      }

      // Clean up
      await payload.delete({
        collection: 'payments',
        id: payment.id,
        user: adminUser,
      })
    })
  })

  describe('Deposits Collection RBAC', () => {
    it('Admin should have full access to deposits', async () => {
      // Admin can create deposits
      const deposit = await payload.create({
        collection: 'deposits',
        data: {
          patient: testPatient.id,
          amount: 1000.00,
          status: 'active',
          receivedBy: adminUser.id,
          notes: 'Admin押金测试',
        },
        user: adminUser,
      })
      expect(deposit.id).toBeDefined()
      testDeposit = deposit

      // Admin can update deposits
      const updatedDeposit = await payload.update({
        collection: 'deposits',
        id: deposit.id,
        data: { status: 'expired' },
        user: adminUser,
      })
      expect(updatedDeposit.status).toBe('expired')

      // Admin can delete deposits
      await payload.delete({
        collection: 'deposits',
        id: deposit.id,
        user: adminUser,
      })
      testDeposit = null
    })

    it('Doctor should have read-only access to deposits', async () => {
      // Create deposit as admin first
      const deposit = await payload.create({
        collection: 'deposits',
        data: {
          patient: testPatient.id,
          amount: 500.00,
          status: 'active',
          receivedBy: adminUser.id,
          notes: 'Doctor押金测试',
        },
        user: adminUser,
      })

      // Doctor can read deposits
      const readDeposit = await payload.findByID({
        collection: 'deposits',
        id: deposit.id,
        user: doctorUser,
      })
      expect(readDeposit.id).toBe(deposit.id)

      // Doctor cannot create deposits
      try {
        await payload.create({
          collection: 'deposits',
          data: {
            patient: testPatient.id,
            amount: 300.00,
            status: 'active',
            receivedBy: doctorUser.id,
          },
          user: doctorUser,
        })
        expect(true).toBe(false) // Should not reach here
      } catch (error) {
        expect(error).toBeDefined()
      }

      // Doctor cannot update deposits
      try {
        await payload.update({
          collection: 'deposits',
          id: deposit.id,
          data: { status: 'expired' },
          user: doctorUser,
        })
        expect(true).toBe(false) // Should not reach here
      } catch (error) {
        expect(error).toBeDefined()
      }

      // Clean up
      await payload.delete({
        collection: 'deposits',
        id: deposit.id,
        user: adminUser,
      })
    })

    it('Front-desk should have create/update access but not delete', async () => {
      // Front-desk can create deposits
      const deposit = await payload.create({
        collection: 'deposits',
        data: {
          patient: testPatient.id,
          amount: 800.00,
          status: 'active',
          receivedBy: frontDeskUser.id,
          notes: 'Front-desk押金测试',
        },
        user: frontDeskUser,
      })
      expect(deposit.id).toBeDefined()

      // Front-desk can update deposits
      const updatedDeposit = await payload.update({
        collection: 'deposits',
        id: deposit.id,
        data: { notes: '更新的押金备注' },
        user: frontDeskUser,
      })
      expect(updatedDeposit.notes).toBe('更新的押金备注')

      // Front-desk cannot delete deposits
      try {
        await payload.delete({
          collection: 'deposits',
          id: deposit.id,
          user: frontDeskUser,
        })
        expect(true).toBe(false) // Should not reach here
      } catch (error) {
        expect(error).toBeDefined()
      }

      // Clean up
      await payload.delete({
        collection: 'deposits',
        id: deposit.id,
        user: adminUser,
      })
    })
  })

  describe('Financial Reports RBAC', () => {
    it('Admin should have access to all financial reports', async () => {
      // This would test API endpoints, but since we're testing Payload directly,
      // we'll test the underlying data access permissions

      // Admin can access all bills for reporting
      const bills = await payload.find({
        collection: 'bills',
        user: adminUser,
        limit: 1000,
      })
      expect(bills.docs).toBeDefined()

      // Admin can access all payments for reporting
      const payments = await payload.find({
        collection: 'payments',
        user: adminUser,
        limit: 1000,
      })
      expect(payments.docs).toBeDefined()

      // Admin can access all deposits for reporting
      const deposits = await payload.find({
        collection: 'deposits',
        user: adminUser,
        limit: 1000,
      })
      expect(deposits.docs).toBeDefined()
    })

    it('Doctor should have limited access to financial data', async () => {
      // Doctor can read bills (for patient billing view)
      const bills = await payload.find({
        collection: 'bills',
        user: doctorUser,
        limit: 10,
      })
      expect(bills.docs).toBeDefined()

      // Doctor can read payments (for patient billing view)
      const payments = await payload.find({
        collection: 'payments',
        user: doctorUser,
        limit: 10,
      })
      expect(payments.docs).toBeDefined()
    })

    it('Front-desk should have access to operational financial data', async () => {
      // Front-desk can access bills for payment processing
      const bills = await payload.find({
        collection: 'bills',
        user: frontDeskUser,
        limit: 100,
      })
      expect(bills.docs).toBeDefined()

      // Front-desk can access payments for processing
      const payments = await payload.find({
        collection: 'payments',
        user: frontDeskUser,
        limit: 100,
      })
      expect(payments.docs).toBeDefined()

      // Front-desk can access deposits for management
      const deposits = await payload.find({
        collection: 'deposits',
        user: frontDeskUser,
        limit: 100,
      })
      expect(deposits.docs).toBeDefined()
    })
  })

  describe('Cross-Collection Permission Tests', () => {
    it('Should enforce patient data access based on role', async () => {
      // All roles should be able to read patient data (needed for billing)
      const patient = await payload.findByID({
        collection: 'patients',
        id: testPatient.id,
        user: doctorUser,
      })
      expect(patient.id).toBe(testPatient.id)

      const patientFrontDesk = await payload.findByID({
        collection: 'patients',
        id: testPatient.id,
        user: frontDeskUser,
      })
      expect(patientFrontDesk.id).toBe(testPatient.id)
    })

    it('Should enforce user management permissions', async () => {
      // Only admin should be able to create users
      try {
        await payload.create({
          collection: 'users',
          data: {
            email: `test-${Date.now()}@clinic.com`,
            role: 'doctor',
            firstName: 'Test',
            lastName: 'User',
            clerkId: `clerk_test_${Date.now()}`,
          },
          user: doctorUser,
        })
        expect(true).toBe(false) // Should not reach here
      } catch (error) {
        expect(error).toBeDefined()
      }

      try {
        await payload.create({
          collection: 'users',
          data: {
            email: `test-${Date.now()}@clinic.com`,
            role: 'doctor',
            firstName: 'Test',
            lastName: 'User',
            clerkId: `clerk_test_${Date.now()}`,
          },
          user: frontDeskUser,
        })
        expect(true).toBe(false) // Should not reach here
      } catch (error) {
        expect(error).toBeDefined()
      }
    })
  })
})
