import { NextRequest, NextResponse } from 'next/server'
import { authenticateWithPayload, makeAuthenticatedPayloadRequest } from '../../../../lib/payload-auth-middleware'

/**
 * GET /api/reports/monthly-revenue - Generate monthly revenue report
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check permissions - only admin and front-desk can view financial reports
    if (!['admin', 'front-desk'].includes(authContext.user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view financial reports' },
        { status: 403 }
      );
    }

    const url = new URL(request.url);
    const yearParam = url.searchParams.get('year');
    const monthParam = url.searchParams.get('month');
    
    if (!yearParam || !monthParam) {
      return NextResponse.json(
        { error: 'Year and month parameters are required' },
        { status: 400 }
      );
    }

    const year = parseInt(yearParam);
    const month = parseInt(monthParam);

    if (isNaN(year) || isNaN(month) || month < 1 || month > 12) {
      return NextResponse.json(
        { error: 'Invalid year or month. Month should be 1-12' },
        { status: 400 }
      );
    }

    // Set date range for the specific month
    const startOfMonth = new Date(year, month - 1, 1);
    const endOfMonth = new Date(year, month, 0, 23, 59, 59, 999);

    const dateFilter = {
      paymentDate: {
        greater_than_equal: startOfMonth.toISOString(),
        less_than_equal: endOfMonth.toISOString(),
      },
      paymentStatus: {
        equals: 'completed'
      }
    };

    // Get payments for the month
    const payments = await makeAuthenticatedPayloadRequest(
      authContext,
      'payments',
      'find',
      {
        where: dateFilter,
        limit: 10000,
        depth: 2,
      }
    );

    // Calculate total revenue
    const totalRevenue = (payments as any).docs.reduce((sum: number, payment: any) => sum + payment.amount, 0);

    // Create daily breakdown
    const dailyBreakdown: Array<{ date: string; revenue: number }> = [];
    const dailyTotals: Record<string, number> = {};

    // Initialize all days in the month
    const daysInMonth = endOfMonth.getDate();
    for (let day = 1; day <= daysInMonth; day++) {
      const dateStr = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
      dailyTotals[dateStr] = 0;
    }

    // Aggregate payments by day
    (payments as any).docs.forEach((payment: any) => {
      const paymentDate = new Date(payment.paymentDate);
      const dateStr = paymentDate.toISOString().split('T')[0];
      if (dailyTotals.hasOwnProperty(dateStr)) {
        dailyTotals[dateStr] += payment.amount;
      }
    });

    // Convert to array format
    Object.entries(dailyTotals).forEach(([date, revenue]) => {
      dailyBreakdown.push({ date, revenue });
    });

    // Sort by date
    dailyBreakdown.sort((a, b) => a.date.localeCompare(b.date));

    const reportData = {
      year,
      month,
      totalRevenue,
      paymentCount: (payments as any).totalDocs,
      dailyBreakdown,
      averageDailyRevenue: daysInMonth > 0 ? totalRevenue / daysInMonth : 0,
      generatedAt: new Date().toISOString(),
      generatedBy: {
        firstName: (authContext.user as any).firstName,
        lastName: (authContext.user as any).lastName,
        email: (authContext.user as any).email,
      },
    };

    return NextResponse.json({
      success: true,
      ...reportData,
    });

  } catch (error) {
    console.error('Error generating monthly revenue report:', error);
    return NextResponse.json(
      { error: 'Failed to generate monthly revenue report' },
      { status: 500 }
    );
  }
}
