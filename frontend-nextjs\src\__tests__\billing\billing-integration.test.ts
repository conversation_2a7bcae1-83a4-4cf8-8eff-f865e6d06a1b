// Integration tests for billing system
// Tests end-to-end workflows, API integration, and business logic

import { describe, it, expect, jest, beforeEach, afterEach, beforeAll, afterAll } from '@jest/globals';
import { NextRequest, NextResponse } from 'next/server';
import { GET as getBills, POST as createBill } from '@/app/api/bills/route';
import { POST as createPayment } from '@/app/api/payments/route';
import { auditLogger, rateLimiter } from '@/lib/billing-security';
import { billingCache } from '@/lib/billing-performance';

// Mock external dependencies
jest.mock('@clerk/nextjs/server', () => ({
  auth: jest.fn(),
}));

jest.mock('@/lib/billing-security', () => ({
  auditLogger: {
    logFinancialOperation: jest.fn(),
  },
  rateLimiter: {
    checkRateLimit: jest.fn(),
  },
  InputSanitizer: {
    sanitizeAmount: jest.fn((amount) => parseFloat(amount)),
    sanitizeText: jest.fn((text) => text.trim()),
    sanitizePaymentMethod: jest.fn((method) => method),
  },
}));

jest.mock('@/lib/billing-performance', () => ({
  billingCache: {
    get: jest.fn(),
    set: jest.fn(),
    invalidatePattern: jest.fn(),
  },
}));

// Mock fetch for backend requests
global.fetch = jest.fn();

describe('Billing System Integration Tests', () => {
  const mockAuth = require('@clerk/nextjs/server').auth;
  const mockFetch = fetch as jest.MockedFunction<typeof fetch>;
  const mockAuditLogger = auditLogger as jest.Mocked<typeof auditLogger>;
  const mockRateLimiter = rateLimiter as jest.Mocked<typeof rateLimiter>;
  const mockCache = billingCache as jest.Mocked<typeof billingCache>;

  const mockUser = {
    userId: 'user-123',
    email_addresses: [{ email_address: '<EMAIL>' }],
  };

  beforeAll(() => {
    // Set up environment variables
    process.env.NEXT_PUBLIC_API_URL = 'http://localhost:8002';
    process.env.CLERK_SECRET_KEY = 'test-secret-key';
  });

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Default mocks
    mockAuth.mockResolvedValue({ userId: mockUser.userId });
    mockRateLimiter.checkRateLimit.mockReturnValue({ allowed: true });
    mockCache.get.mockReturnValue(undefined);
    
    // Mock Clerk user fetch
    mockFetch.mockImplementation((url) => {
      if (url.toString().includes('api.clerk.com')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockUser),
        } as Response);
      }
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve({}),
      } as Response);
    });
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('Bill Management Workflow', () => {
    const mockBillData = {
      patient: 'patient-123',
      billType: 'consultation',
      description: 'Medical consultation',
      items: [
        {
          itemName: 'Consultation Fee',
          quantity: 1,
          unitPrice: 100.00,
          discountRate: 0,
        },
      ],
      subtotal: 100.00,
      totalAmount: 100.00,
      dueDate: '2024-12-31',
    };

    const mockCreatedBill = {
      id: 'bill-123',
      billNumber: 'BILL-001',
      ...mockBillData,
      status: 'pending',
      createdAt: '2024-01-01T00:00:00Z',
    };

    it('should create a bill successfully with full workflow', async () => {
      // Mock backend response for bill creation
      mockFetch.mockImplementationOnce((url) => {
        if (url.toString().includes('api.clerk.com')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve(mockUser),
          } as Response);
        }
        if (url.toString().includes('/api/bills')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve(mockCreatedBill),
          } as Response);
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({}),
        } as Response);
      });

      const request = new NextRequest('http://localhost:3000/api/bills', {
        method: 'POST',
        body: JSON.stringify(mockBillData),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await createBill(request);
      const result = await response.json();

      expect(response.status).toBe(201);
      expect(result).toEqual(mockCreatedBill);
      
      // Verify audit logging
      expect(mockAuditLogger.logFinancialOperation).toHaveBeenCalledWith(
        mockUser.userId,
        mockUser.email_addresses[0].email_address,
        'CREATE_BILL',
        'bills',
        expect.objectContaining({
          billId: mockCreatedBill.id,
          patientId: mockBillData.patient,
          billType: mockBillData.billType,
        }),
        true,
        undefined,
        request
      );

      // Verify cache invalidation
      expect(mockCache.invalidatePattern).toHaveBeenCalledWith('bills_.*');
    });

    it('should handle bill creation validation errors', async () => {
      const invalidBillData = {
        // Missing required fields
        items: [],
      };

      const request = new NextRequest('http://localhost:3000/api/bills', {
        method: 'POST',
        body: JSON.stringify(invalidBillData),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await createBill(request);
      const result = await response.json();

      expect(response.status).toBe(400);
      expect(result.code).toBe('VALIDATION_ERROR');
      
      // Verify audit logging for failed validation
      expect(mockAuditLogger.logFinancialOperation).toHaveBeenCalledWith(
        mockUser.userId,
        mockUser.email_addresses[0].email_address,
        'CREATE_BILL_VALIDATION_FAILED',
        'bills',
        expect.any(Object),
        false,
        'Validation failed',
        request
      );
    });

    it('should fetch bills with caching', async () => {
      const mockBillsResponse = {
        docs: [mockCreatedBill],
        totalDocs: 1,
        page: 1,
        limit: 20,
        totalPages: 1,
      };

      // First request - cache miss
      mockCache.get.mockReturnValueOnce(undefined);
      mockFetch.mockImplementationOnce((url) => {
        if (url.toString().includes('api.clerk.com')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve(mockUser),
          } as Response);
        }
        if (url.toString().includes('/api/bills')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve(mockBillsResponse),
          } as Response);
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({}),
        } as Response);
      });

      const request = new NextRequest('http://localhost:3000/api/bills?page=1&limit=20');
      const response = await getBills(request);
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result).toEqual(mockBillsResponse);
      
      // Verify caching
      expect(mockCache.set).toHaveBeenCalled();
      
      // Verify audit logging
      expect(mockAuditLogger.logFinancialOperation).toHaveBeenCalledWith(
        mockUser.userId,
        mockUser.email_addresses[0].email_address,
        'GET_BILLS',
        'bills',
        expect.objectContaining({
          endpoint: '/api/bills',
          resultCount: 1,
        }),
        true,
        undefined,
        request
      );
    });
  });

  describe('Payment Processing Workflow', () => {
    const mockPaymentData = {
      billId: 'bill-123',
      amount: 100.00,
      paymentMethod: 'cash',
      transactionId: 'TXN-001',
      notes: 'Cash payment for consultation',
    };

    const mockCreatedPayment = {
      id: 'payment-123',
      ...mockPaymentData,
      status: 'completed',
      createdAt: '2024-01-01T00:00:00Z',
    };

    const mockBill = {
      id: 'bill-123',
      totalAmount: 100.00,
      paidAmount: 0,
      status: 'pending',
    };

    it('should process payment successfully with bill validation', async () => {
      // Mock backend responses
      mockFetch.mockImplementation((url) => {
        if (url.toString().includes('api.clerk.com')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve(mockUser),
          } as Response);
        }
        if (url.toString().includes('/api/bills/bill-123')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve(mockBill),
          } as Response);
        }
        if (url.toString().includes('/api/payments')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve(mockCreatedPayment),
          } as Response);
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({}),
        } as Response);
      });

      const request = new NextRequest('http://localhost:3000/api/payments', {
        method: 'POST',
        body: JSON.stringify(mockPaymentData),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await createPayment(request);
      const result = await response.json();

      expect(response.status).toBe(201);
      expect(result).toEqual(mockCreatedPayment);
      
      // Verify audit logging
      expect(mockAuditLogger.logFinancialOperation).toHaveBeenCalledWith(
        mockUser.userId,
        mockUser.email_addresses[0].email_address,
        'CREATE_PAYMENT',
        'payments',
        expect.objectContaining({
          paymentId: mockCreatedPayment.id,
          billId: mockPaymentData.billId,
          amount: mockPaymentData.amount,
          paymentMethod: mockPaymentData.paymentMethod,
        }),
        true,
        undefined,
        request
      );
    });

    it('should handle payment rate limiting', async () => {
      mockRateLimiter.checkRateLimit.mockReturnValue({
        allowed: false,
        resetTime: Date.now() + 60000,
      });

      const request = new NextRequest('http://localhost:3000/api/payments', {
        method: 'POST',
        body: JSON.stringify(mockPaymentData),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await createPayment(request);
      const result = await response.json();

      expect(response.status).toBe(429);
      expect(result.code).toBe('PAYMENT_RATE_LIMIT_EXCEEDED');
      
      // Verify audit logging for rate limiting
      expect(mockAuditLogger.logFinancialOperation).toHaveBeenCalledWith(
        mockUser.userId,
        'unknown',
        'CREATE_PAYMENT_RATE_LIMITED',
        'payments',
        expect.any(Object),
        false,
        'Payment rate limit exceeded',
        request
      );
    });

    it('should validate payment amount against bill', async () => {
      const invalidPaymentData = {
        ...mockPaymentData,
        amount: 200.00, // More than bill total
      };

      // Mock bill fetch
      mockFetch.mockImplementation((url) => {
        if (url.toString().includes('api.clerk.com')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve(mockUser),
          } as Response);
        }
        if (url.toString().includes('/api/bills/bill-123')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve(mockBill),
          } as Response);
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({}),
        } as Response);
      });

      const request = new NextRequest('http://localhost:3000/api/payments', {
        method: 'POST',
        body: JSON.stringify(invalidPaymentData),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await createPayment(request);
      const result = await response.json();

      expect(response.status).toBe(400);
      expect(result.code).toBe('INVALID_PAYMENT_AMOUNT');
    });
  });

  describe('Authentication and Authorization', () => {
    it('should reject unauthenticated requests', async () => {
      mockAuth.mockResolvedValue({ userId: null });

      const request = new NextRequest('http://localhost:3000/api/bills');
      const response = await getBills(request);
      const result = await response.json();

      expect(response.status).toBe(401);
      expect(result.code).toBe('AUTH_REQUIRED');
      
      // Verify audit logging for unauthorized access
      expect(mockAuditLogger.logFinancialOperation).toHaveBeenCalledWith(
        'anonymous',
        'anonymous',
        'GET_BILLS_UNAUTHORIZED',
        'bills',
        expect.any(Object),
        false,
        'Authentication required',
        request
      );
    });

    it('should handle Clerk service errors', async () => {
      mockAuth.mockResolvedValue({ userId: mockUser.userId });
      
      // Mock Clerk API failure
      mockFetch.mockImplementationOnce((url) => {
        if (url.toString().includes('api.clerk.com')) {
          return Promise.resolve({
            ok: false,
            status: 500,
          } as Response);
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({}),
        } as Response);
      });

      const request = new NextRequest('http://localhost:3000/api/bills');
      const response = await getBills(request);
      const result = await response.json();

      expect(response.status).toBe(503);
      expect(result.code).toBe('AUTH_SERVICE_ERROR');
    });
  });

  describe('Error Handling and Recovery', () => {
    it('should handle backend service unavailability', async () => {
      // Mock backend service failure
      mockFetch.mockImplementation((url) => {
        if (url.toString().includes('api.clerk.com')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve(mockUser),
          } as Response);
        }
        if (url.toString().includes('localhost:8002')) {
          return Promise.reject(new Error('Connection refused'));
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({}),
        } as Response);
      });

      const request = new NextRequest('http://localhost:3000/api/bills');
      const response = await getBills(request);
      const result = await response.json();

      expect(response.status).toBe(503);
      expect(result.code).toBe('BACKEND_SERVICE_ERROR');
    });

    it('should handle malformed request data', async () => {
      const request = new NextRequest('http://localhost:3000/api/bills', {
        method: 'POST',
        body: 'invalid json',
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await createBill(request);
      const result = await response.json();

      expect(response.status).toBe(400);
      expect(result.code).toBe('INVALID_JSON');
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle high request volume with rate limiting', async () => {
      const requests = Array.from({ length: 100 }, (_, i) => 
        new NextRequest(`http://localhost:3000/api/bills?page=${i + 1}`)
      );

      // Simulate rate limiting after 60 requests
      let requestCount = 0;
      mockRateLimiter.checkRateLimit.mockImplementation(() => {
        requestCount++;
        return {
          allowed: requestCount <= 60,
          resetTime: requestCount > 60 ? Date.now() + 60000 : undefined,
        };
      });

      const responses = await Promise.all(
        requests.map(request => getBills(request))
      );

      const successfulResponses = responses.filter(r => r.status === 200);
      const rateLimitedResponses = responses.filter(r => r.status === 429);

      expect(successfulResponses.length).toBeLessThanOrEqual(60);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });

    it('should use caching effectively for repeated requests', async () => {
      const mockResponse = { docs: [], totalDocs: 0 };
      
      // First request - cache miss
      mockCache.get.mockReturnValueOnce(undefined);
      mockFetch.mockImplementationOnce((url) => {
        if (url.toString().includes('api.clerk.com')) {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve(mockUser),
          } as Response);
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockResponse),
        } as Response);
      });

      const request1 = new NextRequest('http://localhost:3000/api/bills');
      await getBills(request1);

      expect(mockCache.set).toHaveBeenCalled();

      // Second request - cache hit
      mockCache.get.mockReturnValueOnce(mockResponse);
      mockFetch.mockClear();

      const request2 = new NextRequest('http://localhost:3000/api/bills');
      const response2 = await getBills(request2);
      const result2 = await response2.json();

      expect(result2).toEqual(mockResponse);
      expect(mockFetch).not.toHaveBeenCalledWith(
        expect.stringContaining('localhost:8002')
      );
    });
  });
});
