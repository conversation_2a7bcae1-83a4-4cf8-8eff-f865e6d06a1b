// Unit tests for billing API client
// Tests API functions, error handling, caching, and performance optimizations

import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';
import { billingAPI } from '@/lib/api/billing';
import { billingCache, performanceMonitor } from '@/lib/billing-performance';
import { auditLogger } from '@/lib/billing-security';

// Mock fetch globally
global.fetch = jest.fn();

// Mock the billing modules
jest.mock('@/lib/billing-performance', () => ({
  billingCache: {
    get: jest.fn(),
    set: jest.fn(),
    delete: jest.fn(),
    clear: jest.fn(),
    getStats: jest.fn(),
    invalidatePattern: jest.fn(),
  },
  performanceMonitor: {
    startTiming: jest.fn(() => jest.fn()),
    recordMetric: jest.fn(),
    getStats: jest.fn(),
    getMemoryStats: jest.fn(),
  },
}));

jest.mock('@/lib/billing-security', () => ({
  auditLogger: {
    logFinancialOperation: jest.fn(),
  },
}));

describe('Billing API Client', () => {
  const mockFetch = fetch as jest.MockedFunction<typeof fetch>;
  const mockCache = billingCache as jest.Mocked<typeof billingCache>;
  const mockPerformanceMonitor = performanceMonitor as jest.Mocked<typeof performanceMonitor>;
  const mockAuditLogger = auditLogger as jest.Mocked<typeof auditLogger>;

  beforeEach(() => {
    jest.clearAllMocks();
    mockPerformanceMonitor.startTiming.mockReturnValue(jest.fn());
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('fetchBills', () => {
    const mockBillsResponse = {
      docs: [
        {
          id: '1',
          billNumber: 'BILL-001',
          patient: { id: 'patient-1', fullName: 'John Doe' },
          totalAmount: 100.00,
          status: 'pending',
          createdAt: '2024-01-01T00:00:00Z',
        },
        {
          id: '2',
          billNumber: 'BILL-002',
          patient: { id: 'patient-2', fullName: 'Jane Smith' },
          totalAmount: 200.00,
          status: 'paid',
          createdAt: '2024-01-02T00:00:00Z',
        },
      ],
      totalDocs: 2,
      page: 1,
      limit: 20,
      totalPages: 1,
    };

    it('should fetch bills successfully', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockBillsResponse,
      } as Response);

      const result = await billingAPI.fetchBills();

      expect(mockFetch).toHaveBeenCalledWith('/api/bills', {
        headers: { 'Content-Type': 'application/json' },
      });
      expect(result).toEqual(mockBillsResponse);
    });

    it('should handle fetch bills with parameters', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockBillsResponse,
      } as Response);

      const params = {
        page: 2,
        limit: 10,
        search: 'John',
        status: 'pending',
      };

      await billingAPI.fetchBills(params);

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/bills?page=2&limit=10&search=John&status=pending',
        {
          headers: { 'Content-Type': 'application/json' },
        }
      );
    });

    it('should handle API errors properly', async () => {
      const errorResponse = {
        error: 'Bills not found',
        code: 'BILLS_NOT_FOUND',
        message: '账单未找到',
      };

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: async () => errorResponse,
      } as Response);

      await expect(billingAPI.fetchBills()).rejects.toThrow('Bills not found');
    });

    it('should handle network errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(billingAPI.fetchBills()).rejects.toThrow('Network error occurred');
    });

    it('should use caching for repeated requests', async () => {
      // First call - cache miss
      mockCache.get.mockReturnValueOnce(undefined);
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockBillsResponse,
      } as Response);

      await billingAPI.fetchBills();

      expect(mockCache.get).toHaveBeenCalled();
      expect(mockFetch).toHaveBeenCalled();

      // Second call - cache hit
      mockCache.get.mockReturnValueOnce(mockBillsResponse);
      mockFetch.mockClear();

      const result = await billingAPI.fetchBills();

      expect(mockCache.get).toHaveBeenCalled();
      expect(mockFetch).not.toHaveBeenCalled();
      expect(result).toEqual(mockBillsResponse);
    });
  });

  describe('createBill', () => {
    const mockBillData = {
      patient: 'patient-1',
      billType: 'consultation',
      description: 'Medical consultation',
      items: [
        {
          itemName: 'Consultation Fee',
          quantity: 1,
          unitPrice: 100.00,
          discountRate: 0,
        },
      ],
      subtotal: 100.00,
      totalAmount: 100.00,
    };

    const mockCreatedBill = {
      id: 'bill-1',
      billNumber: 'BILL-001',
      ...mockBillData,
      status: 'pending',
      createdAt: '2024-01-01T00:00:00Z',
    };

    it('should create bill successfully', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 201,
        json: async () => mockCreatedBill,
      } as Response);

      const result = await billingAPI.createBill(mockBillData);

      expect(mockFetch).toHaveBeenCalledWith('/api/bills', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(mockBillData),
      });
      expect(result).toEqual(mockCreatedBill);
    });

    it('should handle validation errors', async () => {
      const validationError = {
        error: 'Validation failed',
        code: 'VALIDATION_ERROR',
        message: '账单数据验证失败',
        details: [
          { field: 'patient', message: 'Patient is required' },
          { field: 'items', message: 'At least one item is required' },
        ],
      };

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: async () => validationError,
      } as Response);

      await expect(billingAPI.createBill({})).rejects.toThrow('Validation failed');
    });

    it('should invalidate cache after creating bill', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 201,
        json: async () => mockCreatedBill,
      } as Response);

      await billingAPI.createBill(mockBillData);

      expect(mockCache.invalidatePattern).toHaveBeenCalledWith('bills_.*');
    });
  });

  describe('createPayment', () => {
    const mockPaymentData = {
      billId: 'bill-1',
      amount: 100.00,
      paymentMethod: 'cash',
      transactionId: 'TXN-001',
      notes: 'Cash payment',
    };

    const mockCreatedPayment = {
      id: 'payment-1',
      ...mockPaymentData,
      status: 'completed',
      createdAt: '2024-01-01T00:00:00Z',
    };

    it('should create payment successfully', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 201,
        json: async () => mockCreatedPayment,
      } as Response);

      const result = await billingAPI.createPayment(mockPaymentData);

      expect(mockFetch).toHaveBeenCalledWith('/api/payments', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(mockPaymentData),
      });
      expect(result).toEqual(mockCreatedPayment);
    });

    it('should handle payment method validation', async () => {
      const invalidPaymentData = {
        ...mockPaymentData,
        paymentMethod: 'invalid-method',
      };

      const validationError = {
        error: 'Invalid payment method',
        code: 'PAYMENT_METHOD_ERROR',
        message: '支付方式验证失败',
      };

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: async () => validationError,
      } as Response);

      await expect(billingAPI.createPayment(invalidPaymentData))
        .rejects.toThrow('Invalid payment method');
    });

    it('should handle rate limiting', async () => {
      const rateLimitError = {
        error: 'Payment rate limit exceeded',
        code: 'PAYMENT_RATE_LIMIT_EXCEEDED',
        message: '支付请求过于频繁，请稍后重试',
        resetTime: Date.now() + 60000,
      };

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 429,
        json: async () => rateLimitError,
      } as Response);

      await expect(billingAPI.createPayment(mockPaymentData))
        .rejects.toThrow('Payment rate limit exceeded');
    });
  });

  describe('Performance Monitoring', () => {
    it('should track performance metrics for API calls', async () => {
      const mockEndTiming = jest.fn();
      mockPerformanceMonitor.startTiming.mockReturnValue(mockEndTiming);

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ docs: [] }),
      } as Response);

      await billingAPI.fetchBills();

      expect(mockPerformanceMonitor.startTiming).toHaveBeenCalledWith('fetchBills');
      expect(mockEndTiming).toHaveBeenCalled();
    });

    it('should track performance even on errors', async () => {
      const mockEndTiming = jest.fn();
      mockPerformanceMonitor.startTiming.mockReturnValue(mockEndTiming);

      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(billingAPI.fetchBills()).rejects.toThrow();

      expect(mockPerformanceMonitor.startTiming).toHaveBeenCalled();
      expect(mockEndTiming).toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should handle malformed JSON responses', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: async () => {
          throw new Error('Invalid JSON');
        },
      } as Response);

      await expect(billingAPI.fetchBills()).rejects.toThrow('HTTP 500: Internal Server Error');
    });

    it('should handle timeout errors', async () => {
      mockFetch.mockImplementationOnce(() => 
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Request timeout')), 100)
        )
      );

      await expect(billingAPI.fetchBills()).rejects.toThrow('Network error occurred');
    });
  });

  describe('Audit Logging', () => {
    it('should log successful operations', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ docs: [] }),
      } as Response);

      await billingAPI.fetchBills();

      // Note: Audit logging happens in the API routes, not the client
      // This test would be more relevant for API route tests
    });
  });
});
