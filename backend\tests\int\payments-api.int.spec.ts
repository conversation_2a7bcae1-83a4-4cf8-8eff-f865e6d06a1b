import { getPayload, Payload } from 'payload'
import config from '@/payload.config'
import { describe, it, beforeAll, afterAll, beforeEach, expect } from 'vitest'

let payload: Payload
let testUser: any
let testPatient: any
let testBill: any

describe('Payments Collection Integration Tests', () => {
  beforeAll(async () => {
    const payloadConfig = await config
    payload = await getPayload({ config: payloadConfig })

    // Create test data
    testUser = await payload.create({
      collection: 'users',
      data: {
        email: `test-${Date.now()}@clinic.com`,
        role: 'admin',
        firstName: 'Test',
        lastName: 'User',
        clerkId: `clerk_test_user_${Date.now()}`,
      },
    })

    testPatient = await payload.create({
      collection: 'patients',
      data: {
        fullName: '测试患者',
        phone: `138${Date.now().toString().slice(-8)}`,
        email: `patient-${Date.now()}@test.com`,
        medicalNotes: '无过敏史',
      },
    })

    testBill = await payload.create({
      collection: 'bills',
      data: {
        patient: testPatient.id,
        billType: 'treatment',
        description: '测试账单',
        subtotal: 500.00,
        discountAmount: 0.00,
        taxAmount: 0.00,
        totalAmount: 500.00,
        remainingAmount: 500.00,
        status: 'confirmed',
        dueDate: new Date('2024-12-31'),
        createdBy: testUser.id,
      },
    })
  })

  afterAll(async () => {
    // Clean up test data
    if (testBill) await payload.delete({ collection: 'bills', id: testBill.id })
    if (testPatient) await payload.delete({ collection: 'patients', id: testPatient.id })
    if (testUser) await payload.delete({ collection: 'users', id: testUser.id })
  })

  describe('Payments Collection', () => {
    let testPayment: any

    beforeEach(async () => {
      // Clean up any existing payments
      const existingPayments = await payload.find({ collection: 'payments' })
      for (const payment of existingPayments.docs) {
        await payload.delete({ collection: 'payments', id: payment.id })
      }
    })

    describe('Create Payment', () => {
      it('should create a new payment successfully', async () => {
        const paymentData = {
          bill: testBill.id,
          patient: testPatient.id,
          amount: 250.00,
          paymentMethod: 'cash',
          paymentStatus: 'completed',
          transactionId: 'TXN-TEST-001',
          paymentDate: new Date(),
          receivedBy: testUser.id,
          notes: '现金支付测试',
        }

        const result = await payload.create({
          collection: 'payments',
          data: paymentData,
        })

        expect(result.id).toBeDefined()
        expect(result.paymentNumber).toMatch(/^PAY-\d{8}-\d{6}$/)
        expect(typeof result.bill === 'object' ? result.bill.id : result.bill).toBe(testBill.id)
        expect(result.amount).toBe(250.00)
        expect(result.paymentStatus).toBe('completed')
        expect(result.receiptNumber).toMatch(/^REC-\d{8}-\d{6}$/)

        testPayment = result
      })

      it('should validate required fields', async () => {
        const invalidPaymentData = {
          // Missing required fields
          amount: 100.00,
          paymentMethod: 'cash',
        }

        try {
          await payload.create({
            collection: 'payments',
            data: invalidPaymentData,
          })
          expect(true).toBe(false) // Should not reach here
        } catch (error) {
          expect(error).toBeDefined()
        }
      })

    })

    describe('Find Payments', () => {
      beforeEach(async () => {
        // Create test payment
        testPayment = await payload.create({
          collection: 'payments',
          data: {
            bill: testBill.id,
            patient: testPatient.id,
            amount: 250.00,
            paymentMethod: 'cash',
            paymentStatus: 'completed',
            transactionId: 'TXN-TEST-001',
            paymentDate: new Date(),
            receivedBy: testUser.id,
            notes: '测试支付',
          },
        })
      })

      it('should fetch payments with pagination', async () => {
        const result = await payload.find({
          collection: 'payments',
          page: 1,
          limit: 10,
        })

        expect(result.docs).toBeDefined()
        expect(result.totalDocs).toBeGreaterThan(0)
        expect(result.page).toBe(1)
        expect(result.limit).toBe(10)
      })

      it('should filter payments by bill ID', async () => {
        const result = await payload.find({
          collection: 'payments',
          where: {
            bill: {
              equals: testBill.id,
            },
          },
        })

        expect(result.docs.every((payment: any) =>
          typeof payment.bill === 'object' ? payment.bill.id === testBill.id : payment.bill === testBill.id
        )).toBe(true)
      })
    })

    describe('Update Payment', () => {
      beforeEach(async () => {
        testPayment = await payload.create({
          collection: 'payments',
          data: {
            bill: testBill.id,
            patient: testPatient.id,
            amount: 250.00,
            paymentMethod: 'cash',
            paymentStatus: 'pending',
            transactionId: 'TXN-TEST-001',
            paymentDate: new Date(),
            receivedBy: testUser.id,
            notes: '测试支付',
          },
        })
      })

      it('should update payment status', async () => {
        const result = await payload.update({
          collection: 'payments',
          id: testPayment.id,
          data: { paymentStatus: 'completed' },
        })

        expect(result.paymentStatus).toBe('completed')
      })
    })
  })
})
