{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/lib/validation/billing-schemas.ts"], "sourcesContent": ["// Comprehensive validation schemas for billing forms\n// Provides robust client-side validation with detailed error messages in Chinese\n\nimport * as z from 'zod';\n\n// Common validation patterns\nconst positiveNumber = z.number().min(0, '金额不能为负数');\nconst requiredString = z.string().min(1, '此字段为必填项');\nconst optionalString = z.string();\nconst phoneRegex = /^1[3-9]\\d{9}$/;\nconst emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n\n// Custom validation functions\nconst validateCurrency = (value: number) => {\n  if (value < 0) return false;\n  // Check for reasonable decimal places (max 2)\n  const decimalPlaces = (value.toString().split('.')[1] || '').length;\n  return decimalPlaces <= 2;\n};\n\nconst validateDateNotInPast = (date: string) => {\n  const inputDate = new Date(date);\n  const today = new Date();\n  today.setHours(0, 0, 0, 0);\n  return inputDate >= today;\n};\n\nconst validateDateNotTooFarInFuture = (date: string) => {\n  const inputDate = new Date(date);\n  const maxDate = new Date();\n  maxDate.setFullYear(maxDate.getFullYear() + 2); // Max 2 years in future\n  return inputDate <= maxDate;\n};\n\n// Bill Item validation schema\nexport const billItemSchema = z.object({\n  itemType: z.enum(['treatment', 'consultation', 'material', 'service'], {\n    required_error: '请选择项目类型',\n    invalid_type_error: '无效的项目类型',\n  }),\n  itemName: requiredString.max(100, '项目名称不能超过100个字符'),\n  description: optionalString.max(500, '描述不能超过500个字符').optional(),\n  quantity: z.number()\n    .min(0.01, '数量必须大于0')\n    .max(9999, '数量不能超过9999')\n    .refine((val) => {\n      const decimalPlaces = (val.toString().split('.')[1] || '').length;\n      return decimalPlaces <= 3;\n    }, '数量最多支持3位小数'),\n  unitPrice: z.number()\n    .min(0, '单价不能为负数')\n    .max(999999.99, '单价不能超过999,999.99')\n    .refine(validateCurrency, '单价格式无效，最多支持2位小数'),\n  discountRate: z.number()\n    .min(0, '折扣率不能为负数')\n    .max(100, '折扣率不能超过100%')\n    .optional(),\n}).refine((data) => {\n  // Validate that discount rate makes sense\n  if (data.discountRate && data.discountRate > 0 && data.unitPrice === 0) {\n    return false;\n  }\n  return true;\n}, {\n  message: '单价为0时不能设置折扣',\n  path: ['discountRate'],\n});\n\n// Bill form validation schema\nexport const billFormSchema = z.object({\n  patient: requiredString.uuid('请选择有效的患者'),\n  appointment: optionalString.uuid('请选择有效的预约').optional().or(z.literal('')),\n  treatment: optionalString.uuid('请选择有效的治疗项目').optional().or(z.literal('')),\n  billType: z.enum(['treatment', 'consultation', 'deposit', 'additional'], {\n    required_error: '请选择账单类型',\n    invalid_type_error: '无效的账单类型',\n  }),\n  description: requiredString\n    .min(2, '账单描述至少需要2个字符')\n    .max(200, '账单描述不能超过200个字符'),\n  notes: optionalString.max(1000, '备注不能超过1000个字符').optional(),\n  dueDate: z.string()\n    .min(1, '请选择到期日期')\n    .refine((date) => {\n      try {\n        new Date(date);\n        return true;\n      } catch {\n        return false;\n      }\n    }, '请输入有效的日期')\n    .refine(validateDateNotInPast, '到期日期不能是过去的日期')\n    .refine(validateDateNotTooFarInFuture, '到期日期不能超过2年'),\n  discountAmount: z.number()\n    .min(0, '折扣金额不能为负数')\n    .max(999999.99, '折扣金额不能超过999,999.99')\n    .refine(validateCurrency, '折扣金额格式无效')\n    .optional(),\n  taxAmount: z.number()\n    .min(0, '税费金额不能为负数')\n    .max(999999.99, '税费金额不能超过999,999.99')\n    .refine(validateCurrency, '税费金额格式无效')\n    .optional(),\n  items: z.array(billItemSchema)\n    .min(1, '至少需要一个账单项目')\n    .max(50, '账单项目不能超过50个'),\n}).refine((data) => {\n  // Validate that bill has reasonable total\n  const itemsTotal = data.items.reduce((sum, item) => {\n    const itemTotal = item.quantity * item.unitPrice;\n    const itemDiscount = itemTotal * ((item.discountRate || 0) / 100);\n    return sum + (itemTotal - itemDiscount);\n  }, 0);\n  \n  const discountAmount = data.discountAmount || 0;\n  const taxAmount = data.taxAmount || 0;\n  const finalTotal = itemsTotal + taxAmount - discountAmount;\n  \n  return finalTotal >= 0;\n}, {\n  message: '账单总金额不能为负数',\n  path: ['discountAmount'],\n}).refine((data) => {\n  // Validate discount doesn't exceed subtotal\n  const itemsTotal = data.items.reduce((sum, item) => {\n    const itemTotal = item.quantity * item.unitPrice;\n    const itemDiscount = itemTotal * ((item.discountRate || 0) / 100);\n    return sum + (itemTotal - itemDiscount);\n  }, 0);\n  \n  const discountAmount = data.discountAmount || 0;\n  return discountAmount <= itemsTotal;\n}, {\n  message: '折扣金额不能超过项目小计',\n  path: ['discountAmount'],\n});\n\n// Payment form validation schema\nexport const paymentFormSchema = z.object({\n  amount: z.number()\n    .min(0.01, '支付金额必须大于0')\n    .max(999999.99, '支付金额不能超过999,999.99')\n    .refine(validateCurrency, '支付金额格式无效，最多支持2位小数'),\n  paymentMethod: z.enum(['cash', 'card', 'wechat', 'alipay', 'transfer', 'installment'], {\n    required_error: '请选择支付方式',\n    invalid_type_error: '无效的支付方式',\n  }),\n  transactionId: z.string()\n    .max(100, '交易ID不能超过100个字符')\n    .optional()\n    .or(z.literal('')),\n  notes: optionalString.max(500, '备注不能超过500个字符').optional(),\n}).refine((data) => {\n  // Require transaction ID for certain payment methods\n  const methodsRequiringTransactionId = ['card', 'wechat', 'alipay', 'transfer'];\n  if (methodsRequiringTransactionId.includes(data.paymentMethod)) {\n    return data.transactionId && data.transactionId.trim().length > 0;\n  }\n  return true;\n}, {\n  message: '此支付方式需要提供交易ID',\n  path: ['transactionId'],\n});\n\n// Patient form validation schema (for billing context)\nexport const patientFormSchema = z.object({\n  fullName: requiredString\n    .min(2, '姓名至少需要2个字符')\n    .max(50, '姓名不能超过50个字符')\n    .regex(/^[\\u4e00-\\u9fa5a-zA-Z\\s]+$/, '姓名只能包含中文、英文和空格'),\n  phone: requiredString\n    .regex(phoneRegex, '请输入有效的手机号码'),\n  email: z.string()\n    .email('请输入有效的邮箱地址')\n    .max(100, '邮箱地址不能超过100个字符')\n    .optional()\n    .or(z.literal('')),\n  medicalNotes: optionalString.max(2000, '医疗备注不能超过2000个字符').optional(),\n});\n\n// Bill status update validation schema\nexport const billStatusUpdateSchema = z.object({\n  status: z.enum(['draft', 'sent', 'confirmed', 'paid', 'cancelled'], {\n    required_error: '请选择账单状态',\n    invalid_type_error: '无效的账单状态',\n  }),\n  notes: optionalString.max(500, '状态更新备注不能超过500个字符').optional(),\n});\n\n// Filter validation schema\nexport const billFilterSchema = z.object({\n  search: optionalString.max(100, '搜索关键词不能超过100个字符').optional(),\n  status: z.enum(['draft', 'sent', 'confirmed', 'paid', 'cancelled']).optional(),\n  billType: z.enum(['treatment', 'consultation', 'deposit', 'additional']).optional(),\n  patientId: optionalString.uuid('请选择有效的患者').optional().or(z.literal('')),\n  dateFrom: z.string()\n    .optional()\n    .refine((date) => {\n      if (!date) return true;\n      try {\n        new Date(date);\n        return true;\n      } catch {\n        return false;\n      }\n    }, '请输入有效的开始日期'),\n  dateTo: z.string()\n    .optional()\n    .refine((date) => {\n      if (!date) return true;\n      try {\n        new Date(date);\n        return true;\n      } catch {\n        return false;\n      }\n    }, '请输入有效的结束日期'),\n  amountMin: z.number()\n    .min(0, '最小金额不能为负数')\n    .max(999999.99, '最小金额不能超过999,999.99')\n    .optional(),\n  amountMax: z.number()\n    .min(0, '最大金额不能为负数')\n    .max(999999.99, '最大金额不能超过999,999.99')\n    .optional(),\n}).refine((data) => {\n  // Validate date range\n  if (data.dateFrom && data.dateTo) {\n    const fromDate = new Date(data.dateFrom);\n    const toDate = new Date(data.dateTo);\n    return fromDate <= toDate;\n  }\n  return true;\n}, {\n  message: '开始日期不能晚于结束日期',\n  path: ['dateTo'],\n}).refine((data) => {\n  // Validate amount range\n  if (data.amountMin !== undefined && data.amountMax !== undefined) {\n    return data.amountMin <= data.amountMax;\n  }\n  return true;\n}, {\n  message: '最小金额不能大于最大金额',\n  path: ['amountMax'],\n});\n\n// Export types for TypeScript\nexport type BillItemFormData = z.infer<typeof billItemSchema>;\nexport type BillFormData = z.infer<typeof billFormSchema>;\nexport type PaymentFormData = z.infer<typeof paymentFormSchema>;\nexport type PatientFormData = z.infer<typeof patientFormSchema>;\nexport type BillStatusUpdateData = z.infer<typeof billStatusUpdateSchema>;\nexport type BillFilterData = z.infer<typeof billFilterSchema>;\n\n// Validation helper functions\nexport const validateBillItem = (data: unknown) => {\n  return billItemSchema.safeParse(data);\n};\n\nexport const validateBillForm = (data: unknown) => {\n  return billFormSchema.safeParse(data);\n};\n\nexport const validatePaymentForm = (data: unknown) => {\n  return paymentFormSchema.safeParse(data);\n};\n\nexport const validatePatientForm = (data: unknown) => {\n  return patientFormSchema.safeParse(data);\n};\n\nexport const validateBillStatusUpdate = (data: unknown) => {\n  return billStatusUpdateSchema.safeParse(data);\n};\n\nexport const validateBillFilter = (data: unknown) => {\n  return billFilterSchema.safeParse(data);\n};\n\n// Custom validation error formatter\nexport const formatValidationErrors = (errors: z.ZodError) => {\n  return errors.errors.map(error => ({\n    field: error.path.join('.'),\n    message: error.message,\n    code: error.code,\n  }));\n};\n"], "names": [], "mappings": "AAAA,qDAAqD;AACrD,iFAAiF;;;;;;;;;;;;;;;;AAEjF;;AAEA,6BAA6B;AAC7B,MAAM,iBAAiB,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;AACzC,MAAM,iBAAiB,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;AACzC,MAAM,iBAAiB,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD;AAC9B,MAAM,aAAa;AACnB,MAAM,aAAa;AAEnB,8BAA8B;AAC9B,MAAM,mBAAmB,CAAC;IACxB,IAAI,QAAQ,GAAG,OAAO;IACtB,8CAA8C;IAC9C,MAAM,gBAAgB,CAAC,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,MAAM;IACnE,OAAO,iBAAiB;AAC1B;AAEA,MAAM,wBAAwB,CAAC;IAC7B,MAAM,YAAY,IAAI,KAAK;IAC3B,MAAM,QAAQ,IAAI;IAClB,MAAM,QAAQ,CAAC,GAAG,GAAG,GAAG;IACxB,OAAO,aAAa;AACtB;AAEA,MAAM,gCAAgC,CAAC;IACrC,MAAM,YAAY,IAAI,KAAK;IAC3B,MAAM,UAAU,IAAI;IACpB,QAAQ,WAAW,CAAC,QAAQ,WAAW,KAAK,IAAI,wBAAwB;IACxE,OAAO,aAAa;AACtB;AAGO,MAAM,iBAAiB,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,EAAE;IACrC,UAAU,CAAA,GAAA,uLAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAa;QAAgB;QAAY;KAAU,EAAE;QACrE,gBAAgB;QAChB,oBAAoB;IACtB;IACA,UAAU,eAAe,GAAG,CAAC,KAAK;IAClC,aAAa,eAAe,GAAG,CAAC,KAAK,gBAAgB,QAAQ;IAC7D,UAAU,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,IACd,GAAG,CAAC,MAAM,WACV,GAAG,CAAC,MAAM,cACV,MAAM,CAAC,CAAC;QACP,MAAM,gBAAgB,CAAC,IAAI,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,MAAM;QACjE,OAAO,iBAAiB;IAC1B,GAAG;IACL,WAAW,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,IACf,GAAG,CAAC,GAAG,WACP,GAAG,CAAC,WAAW,oBACf,MAAM,CAAC,kBAAkB;IAC5B,cAAc,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,IAClB,GAAG,CAAC,GAAG,YACP,GAAG,CAAC,KAAK,eACT,QAAQ;AACb,GAAG,MAAM,CAAC,CAAC;IACT,0CAA0C;IAC1C,IAAI,KAAK,YAAY,IAAI,KAAK,YAAY,GAAG,KAAK,KAAK,SAAS,KAAK,GAAG;QACtE,OAAO;IACT;IACA,OAAO;AACT,GAAG;IACD,SAAS;IACT,MAAM;QAAC;KAAe;AACxB;AAGO,MAAM,iBAAiB,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,EAAE;IACrC,SAAS,eAAe,IAAI,CAAC;IAC7B,aAAa,eAAe,IAAI,CAAC,YAAY,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,uLAAA,CAAA,UAAS,AAAD,EAAE;IACrE,WAAW,eAAe,IAAI,CAAC,cAAc,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,uLAAA,CAAA,UAAS,AAAD,EAAE;IACrE,UAAU,CAAA,GAAA,uLAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAa;QAAgB;QAAW;KAAa,EAAE;QACvE,gBAAgB;QAChB,oBAAoB;IACtB;IACA,aAAa,eACV,GAAG,CAAC,GAAG,gBACP,GAAG,CAAC,KAAK;IACZ,OAAO,eAAe,GAAG,CAAC,MAAM,iBAAiB,QAAQ;IACzD,SAAS,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,IACb,GAAG,CAAC,GAAG,WACP,MAAM,CAAC,CAAC;QACP,IAAI;YACF,IAAI,KAAK;YACT,OAAO;QACT,EAAE,OAAM;YACN,OAAO;QACT;IACF,GAAG,YACF,MAAM,CAAC,uBAAuB,gBAC9B,MAAM,CAAC,+BAA+B;IACzC,gBAAgB,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,IACpB,GAAG,CAAC,GAAG,aACP,GAAG,CAAC,WAAW,sBACf,MAAM,CAAC,kBAAkB,YACzB,QAAQ;IACX,WAAW,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,IACf,GAAG,CAAC,GAAG,aACP,GAAG,CAAC,WAAW,sBACf,MAAM,CAAC,kBAAkB,YACzB,QAAQ;IACX,OAAO,CAAA,GAAA,uLAAA,CAAA,QAAO,AAAD,EAAE,gBACZ,GAAG,CAAC,GAAG,cACP,GAAG,CAAC,IAAI;AACb,GAAG,MAAM,CAAC,CAAC;IACT,0CAA0C;IAC1C,MAAM,aAAa,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK;QACzC,MAAM,YAAY,KAAK,QAAQ,GAAG,KAAK,SAAS;QAChD,MAAM,eAAe,YAAY,CAAC,CAAC,KAAK,YAAY,IAAI,CAAC,IAAI,GAAG;QAChE,OAAO,MAAM,CAAC,YAAY,YAAY;IACxC,GAAG;IAEH,MAAM,iBAAiB,KAAK,cAAc,IAAI;IAC9C,MAAM,YAAY,KAAK,SAAS,IAAI;IACpC,MAAM,aAAa,aAAa,YAAY;IAE5C,OAAO,cAAc;AACvB,GAAG;IACD,SAAS;IACT,MAAM;QAAC;KAAiB;AAC1B,GAAG,MAAM,CAAC,CAAC;IACT,4CAA4C;IAC5C,MAAM,aAAa,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK;QACzC,MAAM,YAAY,KAAK,QAAQ,GAAG,KAAK,SAAS;QAChD,MAAM,eAAe,YAAY,CAAC,CAAC,KAAK,YAAY,IAAI,CAAC,IAAI,GAAG;QAChE,OAAO,MAAM,CAAC,YAAY,YAAY;IACxC,GAAG;IAEH,MAAM,iBAAiB,KAAK,cAAc,IAAI;IAC9C,OAAO,kBAAkB;AAC3B,GAAG;IACD,SAAS;IACT,MAAM;QAAC;KAAiB;AAC1B;AAGO,MAAM,oBAAoB,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,EAAE;IACxC,QAAQ,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,IACZ,GAAG,CAAC,MAAM,aACV,GAAG,CAAC,WAAW,sBACf,MAAM,CAAC,kBAAkB;IAC5B,eAAe,CAAA,GAAA,uLAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAQ;QAAQ;QAAU;QAAU;QAAY;KAAc,EAAE;QACrF,gBAAgB;QAChB,oBAAoB;IACtB;IACA,eAAe,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,IACnB,GAAG,CAAC,KAAK,kBACT,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,uLAAA,CAAA,UAAS,AAAD,EAAE;IAChB,OAAO,eAAe,GAAG,CAAC,KAAK,gBAAgB,QAAQ;AACzD,GAAG,MAAM,CAAC,CAAC;IACT,qDAAqD;IACrD,MAAM,gCAAgC;QAAC;QAAQ;QAAU;QAAU;KAAW;IAC9E,IAAI,8BAA8B,QAAQ,CAAC,KAAK,aAAa,GAAG;QAC9D,OAAO,KAAK,aAAa,IAAI,KAAK,aAAa,CAAC,IAAI,GAAG,MAAM,GAAG;IAClE;IACA,OAAO;AACT,GAAG;IACD,SAAS;IACT,MAAM;QAAC;KAAgB;AACzB;AAGO,MAAM,oBAAoB,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,EAAE;IACxC,UAAU,eACP,GAAG,CAAC,GAAG,cACP,GAAG,CAAC,IAAI,eACR,KAAK,CAAC,8BAA8B;IACvC,OAAO,eACJ,KAAK,CAAC,YAAY;IACrB,OAAO,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,IACX,KAAK,CAAC,cACN,GAAG,CAAC,KAAK,kBACT,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,uLAAA,CAAA,UAAS,AAAD,EAAE;IAChB,cAAc,eAAe,GAAG,CAAC,MAAM,mBAAmB,QAAQ;AACpE;AAGO,MAAM,yBAAyB,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,EAAE;IAC7C,QAAQ,CAAA,GAAA,uLAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAS;QAAQ;QAAa;QAAQ;KAAY,EAAE;QAClE,gBAAgB;QAChB,oBAAoB;IACtB;IACA,OAAO,eAAe,GAAG,CAAC,KAAK,oBAAoB,QAAQ;AAC7D;AAGO,MAAM,mBAAmB,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,EAAE;IACvC,QAAQ,eAAe,GAAG,CAAC,KAAK,mBAAmB,QAAQ;IAC3D,QAAQ,CAAA,GAAA,uLAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAS;QAAQ;QAAa;QAAQ;KAAY,EAAE,QAAQ;IAC5E,UAAU,CAAA,GAAA,uLAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAa;QAAgB;QAAW;KAAa,EAAE,QAAQ;IACjF,WAAW,eAAe,IAAI,CAAC,YAAY,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,uLAAA,CAAA,UAAS,AAAD,EAAE;IACnE,UAAU,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,IACd,QAAQ,GACR,MAAM,CAAC,CAAC;QACP,IAAI,CAAC,MAAM,OAAO;QAClB,IAAI;YACF,IAAI,KAAK;YACT,OAAO;QACT,EAAE,OAAM;YACN,OAAO;QACT;IACF,GAAG;IACL,QAAQ,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,IACZ,QAAQ,GACR,MAAM,CAAC,CAAC;QACP,IAAI,CAAC,MAAM,OAAO;QAClB,IAAI;YACF,IAAI,KAAK;YACT,OAAO;QACT,EAAE,OAAM;YACN,OAAO;QACT;IACF,GAAG;IACL,WAAW,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,IACf,GAAG,CAAC,GAAG,aACP,GAAG,CAAC,WAAW,sBACf,QAAQ;IACX,WAAW,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,IACf,GAAG,CAAC,GAAG,aACP,GAAG,CAAC,WAAW,sBACf,QAAQ;AACb,GAAG,MAAM,CAAC,CAAC;IACT,sBAAsB;IACtB,IAAI,KAAK,QAAQ,IAAI,KAAK,MAAM,EAAE;QAChC,MAAM,WAAW,IAAI,KAAK,KAAK,QAAQ;QACvC,MAAM,SAAS,IAAI,KAAK,KAAK,MAAM;QACnC,OAAO,YAAY;IACrB;IACA,OAAO;AACT,GAAG;IACD,SAAS;IACT,MAAM;QAAC;KAAS;AAClB,GAAG,MAAM,CAAC,CAAC;IACT,wBAAwB;IACxB,IAAI,KAAK,SAAS,KAAK,aAAa,KAAK,SAAS,KAAK,WAAW;QAChE,OAAO,KAAK,SAAS,IAAI,KAAK,SAAS;IACzC;IACA,OAAO;AACT,GAAG;IACD,SAAS;IACT,MAAM;QAAC;KAAY;AACrB;AAWO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,eAAe,SAAS,CAAC;AAClC;AAEO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,eAAe,SAAS,CAAC;AAClC;AAEO,MAAM,sBAAsB,CAAC;IAClC,OAAO,kBAAkB,SAAS,CAAC;AACrC;AAEO,MAAM,sBAAsB,CAAC;IAClC,OAAO,kBAAkB,SAAS,CAAC;AACrC;AAEO,MAAM,2BAA2B,CAAC;IACvC,OAAO,uBAAuB,SAAS,CAAC;AAC1C;AAEO,MAAM,qBAAqB,CAAC;IACjC,OAAO,iBAAiB,SAAS,CAAC;AACpC;AAGO,MAAM,yBAAyB,CAAC;IACrC,OAAO,OAAO,MAAM,CAAC,GAAG,CAAC,CAAA,QAAS,CAAC;YACjC,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC;YACvB,SAAS,MAAM,OAAO;YACtB,MAAM,MAAM,IAAI;QAClB,CAAC;AACH", "debugId": null}}, {"offset": {"line": 359, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/lib/billing-security.ts"], "sourcesContent": ["// Comprehensive security measures for billing system\n// Handles data encryption, audit logging, and secure payment processing\n\nimport crypto from 'crypto';\n\n// Security configuration\nconst SECURITY_CONFIG = {\n  encryption: {\n    algorithm: 'aes-256-gcm',\n    keyLength: 32,\n    ivLength: 16,\n    tagLength: 16,\n  },\n  audit: {\n    maxLogSize: 10000, // Maximum number of audit log entries\n    sensitiveFields: ['cardNumber', 'cvv', 'bankAccount', 'transactionId'],\n  },\n  rateLimit: {\n    maxRequestsPerMinute: 60,\n    maxPaymentRequestsPerHour: 10,\n  },\n};\n\n// Encryption utilities for sensitive financial data\nexport class DataEncryption {\n  private static getEncryptionKey(): string {\n    const key = process.env.BILLING_ENCRYPTION_KEY;\n    if (!key) {\n      throw new Error('BILLING_ENCRYPTION_KEY environment variable is required');\n    }\n    return key;\n  }\n\n  /**\n   * Encrypt sensitive data\n   */\n  static encrypt(data: string): { encrypted: string; iv: string; tag: string } {\n    try {\n      const key = Buffer.from(this.getEncryptionKey(), 'hex');\n      const iv = crypto.randomBytes(SECURITY_CONFIG.encryption.ivLength);\n      const cipher = crypto.createCipher(SECURITY_CONFIG.encryption.algorithm, key);\n      cipher.setAAD(Buffer.from('billing-data'));\n\n      let encrypted = cipher.update(data, 'utf8', 'hex');\n      encrypted += cipher.final('hex');\n      \n      const tag = cipher.getAuthTag();\n\n      return {\n        encrypted,\n        iv: iv.toString('hex'),\n        tag: tag.toString('hex'),\n      };\n    } catch (error) {\n      console.error('Encryption error:', error);\n      throw new Error('Failed to encrypt sensitive data');\n    }\n  }\n\n  /**\n   * Decrypt sensitive data\n   */\n  static decrypt(encryptedData: { encrypted: string; iv: string; tag: string }): string {\n    try {\n      const key = Buffer.from(this.getEncryptionKey(), 'hex');\n      const iv = Buffer.from(encryptedData.iv, 'hex');\n      const tag = Buffer.from(encryptedData.tag, 'hex');\n      \n      const decipher = crypto.createDecipher(SECURITY_CONFIG.encryption.algorithm, key);\n      decipher.setAAD(Buffer.from('billing-data'));\n      decipher.setAuthTag(tag);\n\n      let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');\n      decrypted += decipher.final('utf8');\n\n      return decrypted;\n    } catch (error) {\n      console.error('Decryption error:', error);\n      throw new Error('Failed to decrypt sensitive data');\n    }\n  }\n\n  /**\n   * Hash sensitive data for comparison (one-way)\n   */\n  static hash(data: string): string {\n    return crypto.createHash('sha256').update(data).digest('hex');\n  }\n\n  /**\n   * Generate secure random token\n   */\n  static generateSecureToken(length: number = 32): string {\n    return crypto.randomBytes(length).toString('hex');\n  }\n}\n\n// Audit logging for financial operations\nexport interface AuditLogEntry {\n  id: string;\n  timestamp: Date;\n  userId: string;\n  userEmail: string;\n  action: string;\n  resource: string;\n  resourceId?: string;\n  details: any;\n  ipAddress?: string;\n  userAgent?: string;\n  success: boolean;\n  errorMessage?: string;\n}\n\nexport class AuditLogger {\n  private static instance: AuditLogger;\n  private auditLog: AuditLogEntry[] = [];\n\n  private constructor() {}\n\n  static getInstance(): AuditLogger {\n    if (!AuditLogger.instance) {\n      AuditLogger.instance = new AuditLogger();\n    }\n    return AuditLogger.instance;\n  }\n\n  /**\n   * Log financial operation\n   */\n  logFinancialOperation(\n    userId: string,\n    userEmail: string,\n    action: string,\n    resource: string,\n    details: any,\n    success: boolean = true,\n    errorMessage?: string,\n    request?: Request\n  ): void {\n    const entry: AuditLogEntry = {\n      id: DataEncryption.generateSecureToken(16),\n      timestamp: new Date(),\n      userId,\n      userEmail,\n      action,\n      resource,\n      resourceId: details.id || details.billId || details.paymentId,\n      details: this.sanitizeDetails(details),\n      ipAddress: this.getClientIP(request),\n      userAgent: request?.headers.get('user-agent') || undefined,\n      success,\n      errorMessage,\n    };\n\n    this.auditLog.push(entry);\n\n    // Keep only the most recent entries\n    if (this.auditLog.length > SECURITY_CONFIG.audit.maxLogSize) {\n      this.auditLog.shift();\n    }\n\n    // Log to console for development (in production, this should go to a secure logging service)\n    console.log(`[AUDIT] ${entry.timestamp.toISOString()} - ${entry.action} on ${entry.resource} by ${entry.userEmail} - ${entry.success ? 'SUCCESS' : 'FAILED'}`);\n  }\n\n  /**\n   * Get audit log entries (filtered for security)\n   */\n  getAuditLog(userId?: string, limit: number = 100): AuditLogEntry[] {\n    let filteredLog = this.auditLog;\n\n    if (userId) {\n      filteredLog = filteredLog.filter(entry => entry.userId === userId);\n    }\n\n    return filteredLog\n      .slice(-limit)\n      .map(entry => ({\n        ...entry,\n        details: this.sanitizeDetails(entry.details),\n      }));\n  }\n\n  /**\n   * Remove sensitive information from audit details\n   */\n  private sanitizeDetails(details: any): any {\n    if (!details || typeof details !== 'object') {\n      return details;\n    }\n\n    const sanitized = { ...details };\n    \n    SECURITY_CONFIG.audit.sensitiveFields.forEach(field => {\n      if (sanitized[field]) {\n        sanitized[field] = this.maskSensitiveData(sanitized[field]);\n      }\n    });\n\n    return sanitized;\n  }\n\n  /**\n   * Mask sensitive data for logging\n   */\n  private maskSensitiveData(data: string): string {\n    if (data.length <= 4) {\n      return '****';\n    }\n    return data.substring(0, 2) + '*'.repeat(data.length - 4) + data.substring(data.length - 2);\n  }\n\n  /**\n   * Extract client IP address from request\n   */\n  private getClientIP(request?: Request): string | undefined {\n    if (!request) return undefined;\n\n    const forwarded = request.headers.get('x-forwarded-for');\n    if (forwarded) {\n      return forwarded.split(',')[0].trim();\n    }\n\n    const realIP = request.headers.get('x-real-ip');\n    if (realIP) {\n      return realIP;\n    }\n\n    return 'unknown';\n  }\n}\n\n// Rate limiting for API endpoints\nexport class RateLimiter {\n  private static instance: RateLimiter;\n  private requestCounts: Map<string, { count: number; resetTime: number }> = new Map();\n  private paymentCounts: Map<string, { count: number; resetTime: number }> = new Map();\n\n  private constructor() {}\n\n  static getInstance(): RateLimiter {\n    if (!RateLimiter.instance) {\n      RateLimiter.instance = new RateLimiter();\n    }\n    return RateLimiter.instance;\n  }\n\n  /**\n   * Check if request is within rate limit\n   */\n  checkRateLimit(userId: string, isPaymentRequest: boolean = false): { allowed: boolean; resetTime?: number } {\n    const now = Date.now();\n    const limits = isPaymentRequest \n      ? { max: SECURITY_CONFIG.rateLimit.maxPaymentRequestsPerHour, window: 60 * 60 * 1000 }\n      : { max: SECURITY_CONFIG.rateLimit.maxRequestsPerMinute, window: 60 * 1000 };\n\n    const counts = isPaymentRequest ? this.paymentCounts : this.requestCounts;\n    const userCount = counts.get(userId);\n\n    if (!userCount || now > userCount.resetTime) {\n      // Reset or initialize counter\n      counts.set(userId, { count: 1, resetTime: now + limits.window });\n      return { allowed: true };\n    }\n\n    if (userCount.count >= limits.max) {\n      return { allowed: false, resetTime: userCount.resetTime };\n    }\n\n    userCount.count++;\n    return { allowed: true };\n  }\n\n  /**\n   * Clear expired rate limit entries\n   */\n  cleanup(): void {\n    const now = Date.now();\n    \n    for (const [userId, data] of this.requestCounts.entries()) {\n      if (now > data.resetTime) {\n        this.requestCounts.delete(userId);\n      }\n    }\n\n    for (const [userId, data] of this.paymentCounts.entries()) {\n      if (now > data.resetTime) {\n        this.paymentCounts.delete(userId);\n      }\n    }\n  }\n}\n\n// Input sanitization for financial data\nexport class InputSanitizer {\n  /**\n   * Sanitize and validate monetary amounts\n   */\n  static sanitizeAmount(amount: any): number {\n    if (typeof amount === 'number') {\n      if (!isFinite(amount) || amount < 0) {\n        throw new Error('Invalid amount: must be a positive finite number');\n      }\n      return Math.round(amount * 100) / 100; // Round to 2 decimal places\n    }\n\n    if (typeof amount === 'string') {\n      const parsed = parseFloat(amount.replace(/[^\\d.-]/g, ''));\n      if (isNaN(parsed) || parsed < 0) {\n        throw new Error('Invalid amount: must be a positive number');\n      }\n      return Math.round(parsed * 100) / 100;\n    }\n\n    throw new Error('Invalid amount: must be a number or numeric string');\n  }\n\n  /**\n   * Sanitize text input to prevent XSS and injection attacks\n   */\n  static sanitizeText(text: string, maxLength: number = 1000): string {\n    if (typeof text !== 'string') {\n      throw new Error('Input must be a string');\n    }\n\n    // Remove potentially dangerous characters and HTML tags\n    const sanitized = text\n      .replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, '')\n      .replace(/<[^>]*>/g, '')\n      .replace(/javascript:/gi, '')\n      .replace(/on\\w+\\s*=/gi, '')\n      .trim();\n\n    if (sanitized.length > maxLength) {\n      throw new Error(`Input too long: maximum ${maxLength} characters allowed`);\n    }\n\n    return sanitized;\n  }\n\n  /**\n   * Validate and sanitize payment method\n   */\n  static sanitizePaymentMethod(method: string): string {\n    const validMethods = ['cash', 'card', 'wechat', 'alipay', 'transfer', 'installment'];\n    \n    if (!validMethods.includes(method)) {\n      throw new Error('Invalid payment method');\n    }\n\n    return method;\n  }\n\n  /**\n   * Sanitize transaction ID\n   */\n  static sanitizeTransactionId(transactionId: string): string {\n    if (typeof transactionId !== 'string') {\n      throw new Error('Transaction ID must be a string');\n    }\n\n    // Allow only alphanumeric characters, hyphens, and underscores\n    const sanitized = transactionId.replace(/[^a-zA-Z0-9\\-_]/g, '');\n    \n    if (sanitized.length < 3 || sanitized.length > 100) {\n      throw new Error('Transaction ID must be between 3 and 100 characters');\n    }\n\n    return sanitized;\n  }\n}\n\n// Export singleton instances\nexport const auditLogger = AuditLogger.getInstance();\nexport const rateLimiter = RateLimiter.getInstance();\n\n// Cleanup function to be called periodically\nexport const cleanupSecurity = () => {\n  rateLimiter.cleanup();\n};\n"], "names": [], "mappings": "AAAA,qDAAqD;AACrD,wEAAwE;;;;;;;;;;AAExE;;AAEA,yBAAyB;AACzB,MAAM,kBAAkB;IACtB,YAAY;QACV,WAAW;QACX,WAAW;QACX,UAAU;QACV,WAAW;IACb;IACA,OAAO;QACL,YAAY;QACZ,iBAAiB;YAAC;YAAc;YAAO;YAAe;SAAgB;IACxE;IACA,WAAW;QACT,sBAAsB;QACtB,2BAA2B;IAC7B;AACF;AAGO,MAAM;IACX,OAAe,mBAA2B;QACxC,MAAM,MAAM,QAAQ,GAAG,CAAC,sBAAsB;QAC9C,IAAI,CAAC,KAAK;YACR,MAAM,IAAI,MAAM;QAClB;QACA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,QAAQ,IAAY,EAAkD;QAC3E,IAAI;YACF,MAAM,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI;YACjD,MAAM,KAAK,qGAAA,CAAA,UAAM,CAAC,WAAW,CAAC,gBAAgB,UAAU,CAAC,QAAQ;YACjE,MAAM,SAAS,qGAAA,CAAA,UAAM,CAAC,YAAY,CAAC,gBAAgB,UAAU,CAAC,SAAS,EAAE;YACzE,OAAO,MAAM,CAAC,OAAO,IAAI,CAAC;YAE1B,IAAI,YAAY,OAAO,MAAM,CAAC,MAAM,QAAQ;YAC5C,aAAa,OAAO,KAAK,CAAC;YAE1B,MAAM,MAAM,OAAO,UAAU;YAE7B,OAAO;gBACL;gBACA,IAAI,GAAG,QAAQ,CAAC;gBAChB,KAAK,IAAI,QAAQ,CAAC;YACpB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,OAAO,QAAQ,aAA6D,EAAU;QACpF,IAAI;YACF,MAAM,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI;YACjD,MAAM,KAAK,OAAO,IAAI,CAAC,cAAc,EAAE,EAAE;YACzC,MAAM,MAAM,OAAO,IAAI,CAAC,cAAc,GAAG,EAAE;YAE3C,MAAM,WAAW,qGAAA,CAAA,UAAM,CAAC,cAAc,CAAC,gBAAgB,UAAU,CAAC,SAAS,EAAE;YAC7E,SAAS,MAAM,CAAC,OAAO,IAAI,CAAC;YAC5B,SAAS,UAAU,CAAC;YAEpB,IAAI,YAAY,SAAS,MAAM,CAAC,cAAc,SAAS,EAAE,OAAO;YAChE,aAAa,SAAS,KAAK,CAAC;YAE5B,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,OAAO,KAAK,IAAY,EAAU;QAChC,OAAO,qGAAA,CAAA,UAAM,CAAC,UAAU,CAAC,UAAU,MAAM,CAAC,MAAM,MAAM,CAAC;IACzD;IAEA;;GAEC,GACD,OAAO,oBAAoB,SAAiB,EAAE,EAAU;QACtD,OAAO,qGAAA,CAAA,UAAM,CAAC,WAAW,CAAC,QAAQ,QAAQ,CAAC;IAC7C;AACF;AAkBO,MAAM;IACX,OAAe,SAAsB;IAC7B,WAA4B,EAAE,CAAC;IAEvC,aAAsB,CAAC;IAEvB,OAAO,cAA2B;QAChC,IAAI,CAAC,YAAY,QAAQ,EAAE;YACzB,YAAY,QAAQ,GAAG,IAAI;QAC7B;QACA,OAAO,YAAY,QAAQ;IAC7B;IAEA;;GAEC,GACD,sBACE,MAAc,EACd,SAAiB,EACjB,MAAc,EACd,QAAgB,EAChB,OAAY,EACZ,UAAmB,IAAI,EACvB,YAAqB,EACrB,OAAiB,EACX;QACN,MAAM,QAAuB;YAC3B,IAAI,eAAe,mBAAmB,CAAC;YACvC,WAAW,IAAI;YACf;YACA;YACA;YACA;YACA,YAAY,QAAQ,EAAE,IAAI,QAAQ,MAAM,IAAI,QAAQ,SAAS;YAC7D,SAAS,IAAI,CAAC,eAAe,CAAC;YAC9B,WAAW,IAAI,CAAC,WAAW,CAAC;YAC5B,WAAW,SAAS,QAAQ,IAAI,iBAAiB;YACjD;YACA;QACF;QAEA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QAEnB,oCAAoC;QACpC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,gBAAgB,KAAK,CAAC,UAAU,EAAE;YAC3D,IAAI,CAAC,QAAQ,CAAC,KAAK;QACrB;QAEA,6FAA6F;QAC7F,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,MAAM,SAAS,CAAC,WAAW,GAAG,GAAG,EAAE,MAAM,MAAM,CAAC,IAAI,EAAE,MAAM,QAAQ,CAAC,IAAI,EAAE,MAAM,SAAS,CAAC,GAAG,EAAE,MAAM,OAAO,GAAG,YAAY,UAAU;IAC/J;IAEA;;GAEC,GACD,YAAY,MAAe,EAAE,QAAgB,GAAG,EAAmB;QACjE,IAAI,cAAc,IAAI,CAAC,QAAQ;QAE/B,IAAI,QAAQ;YACV,cAAc,YAAY,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;QAC7D;QAEA,OAAO,YACJ,KAAK,CAAC,CAAC,OACP,GAAG,CAAC,CAAA,QAAS,CAAC;gBACb,GAAG,KAAK;gBACR,SAAS,IAAI,CAAC,eAAe,CAAC,MAAM,OAAO;YAC7C,CAAC;IACL;IAEA;;GAEC,GACD,AAAQ,gBAAgB,OAAY,EAAO;QACzC,IAAI,CAAC,WAAW,OAAO,YAAY,UAAU;YAC3C,OAAO;QACT;QAEA,MAAM,YAAY;YAAE,GAAG,OAAO;QAAC;QAE/B,gBAAgB,KAAK,CAAC,eAAe,CAAC,OAAO,CAAC,CAAA;YAC5C,IAAI,SAAS,CAAC,MAAM,EAAE;gBACpB,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,MAAM;YAC5D;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,kBAAkB,IAAY,EAAU;QAC9C,IAAI,KAAK,MAAM,IAAI,GAAG;YACpB,OAAO;QACT;QACA,OAAO,KAAK,SAAS,CAAC,GAAG,KAAK,IAAI,MAAM,CAAC,KAAK,MAAM,GAAG,KAAK,KAAK,SAAS,CAAC,KAAK,MAAM,GAAG;IAC3F;IAEA;;GAEC,GACD,AAAQ,YAAY,OAAiB,EAAsB;QACzD,IAAI,CAAC,SAAS,OAAO;QAErB,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC;QACtC,IAAI,WAAW;YACb,OAAO,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;QACrC;QAEA,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;QACnC,IAAI,QAAQ;YACV,OAAO;QACT;QAEA,OAAO;IACT;AACF;AAGO,MAAM;IACX,OAAe,SAAsB;IAC7B,gBAAmE,IAAI,MAAM;IAC7E,gBAAmE,IAAI,MAAM;IAErF,aAAsB,CAAC;IAEvB,OAAO,cAA2B;QAChC,IAAI,CAAC,YAAY,QAAQ,EAAE;YACzB,YAAY,QAAQ,GAAG,IAAI;QAC7B;QACA,OAAO,YAAY,QAAQ;IAC7B;IAEA;;GAEC,GACD,eAAe,MAAc,EAAE,mBAA4B,KAAK,EAA4C;QAC1G,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,SAAS,mBACX;YAAE,KAAK,gBAAgB,SAAS,CAAC,yBAAyB;YAAE,QAAQ,KAAK,KAAK;QAAK,IACnF;YAAE,KAAK,gBAAgB,SAAS,CAAC,oBAAoB;YAAE,QAAQ,KAAK;QAAK;QAE7E,MAAM,SAAS,mBAAmB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa;QACzE,MAAM,YAAY,OAAO,GAAG,CAAC;QAE7B,IAAI,CAAC,aAAa,MAAM,UAAU,SAAS,EAAE;YAC3C,8BAA8B;YAC9B,OAAO,GAAG,CAAC,QAAQ;gBAAE,OAAO;gBAAG,WAAW,MAAM,OAAO,MAAM;YAAC;YAC9D,OAAO;gBAAE,SAAS;YAAK;QACzB;QAEA,IAAI,UAAU,KAAK,IAAI,OAAO,GAAG,EAAE;YACjC,OAAO;gBAAE,SAAS;gBAAO,WAAW,UAAU,SAAS;YAAC;QAC1D;QAEA,UAAU,KAAK;QACf,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA;;GAEC,GACD,UAAgB;QACd,MAAM,MAAM,KAAK,GAAG;QAEpB,KAAK,MAAM,CAAC,QAAQ,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,GAAI;YACzD,IAAI,MAAM,KAAK,SAAS,EAAE;gBACxB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;YAC5B;QACF;QAEA,KAAK,MAAM,CAAC,QAAQ,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,GAAI;YACzD,IAAI,MAAM,KAAK,SAAS,EAAE;gBACxB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;YAC5B;QACF;IACF;AACF;AAGO,MAAM;IACX;;GAEC,GACD,OAAO,eAAe,MAAW,EAAU;QACzC,IAAI,OAAO,WAAW,UAAU;YAC9B,IAAI,CAAC,SAAS,WAAW,SAAS,GAAG;gBACnC,MAAM,IAAI,MAAM;YAClB;YACA,OAAO,KAAK,KAAK,CAAC,SAAS,OAAO,KAAK,4BAA4B;QACrE;QAEA,IAAI,OAAO,WAAW,UAAU;YAC9B,MAAM,SAAS,WAAW,OAAO,OAAO,CAAC,YAAY;YACrD,IAAI,MAAM,WAAW,SAAS,GAAG;gBAC/B,MAAM,IAAI,MAAM;YAClB;YACA,OAAO,KAAK,KAAK,CAAC,SAAS,OAAO;QACpC;QAEA,MAAM,IAAI,MAAM;IAClB;IAEA;;GAEC,GACD,OAAO,aAAa,IAAY,EAAE,YAAoB,IAAI,EAAU;QAClE,IAAI,OAAO,SAAS,UAAU;YAC5B,MAAM,IAAI,MAAM;QAClB;QAEA,wDAAwD;QACxD,MAAM,YAAY,KACf,OAAO,CAAC,uDAAuD,IAC/D,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,iBAAiB,IACzB,OAAO,CAAC,eAAe,IACvB,IAAI;QAEP,IAAI,UAAU,MAAM,GAAG,WAAW;YAChC,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,UAAU,mBAAmB,CAAC;QAC3E;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,sBAAsB,MAAc,EAAU;QACnD,MAAM,eAAe;YAAC;YAAQ;YAAQ;YAAU;YAAU;YAAY;SAAc;QAEpF,IAAI,CAAC,aAAa,QAAQ,CAAC,SAAS;YAClC,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,sBAAsB,aAAqB,EAAU;QAC1D,IAAI,OAAO,kBAAkB,UAAU;YACrC,MAAM,IAAI,MAAM;QAClB;QAEA,+DAA+D;QAC/D,MAAM,YAAY,cAAc,OAAO,CAAC,oBAAoB;QAE5D,IAAI,UAAU,MAAM,GAAG,KAAK,UAAU,MAAM,GAAG,KAAK;YAClD,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT;AACF;AAGO,MAAM,cAAc,YAAY,WAAW;AAC3C,MAAM,cAAc,YAAY,WAAW;AAG3C,MAAM,kBAAkB;IAC7B,YAAY,OAAO;AACrB", "debugId": null}}, {"offset": {"line": 672, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/app/api/bills/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { auth } from '@clerk/nextjs/server';\nimport { billFormSchema, billFilterSchema } from '@/lib/validation/billing-schemas';\nimport { formatValidationErrors } from '@/lib/validation/billing-schemas';\nimport { auditLogger, rateLimiter, InputSanitizer } from '@/lib/billing-security';\n\nconst BACKEND_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8002';\n\n// Enhanced error handling utility\nclass APIError extends Error {\n  constructor(\n    message: string,\n    public status: number = 500,\n    public code?: string,\n    public details?: any\n  ) {\n    super(message);\n    this.name = 'APIError';\n  }\n}\n\n// Utility to get user info from Clerk with error handling\nasync function getClerkUserInfo(userId: string) {\n  try {\n    const response = await fetch(`https://api.clerk.com/v1/users/${userId}`, {\n      headers: {\n        Authorization: `Bearer ${process.env.CLERK_SECRET_KEY}`,\n      },\n    });\n\n    if (!response.ok) {\n      throw new APIError('Failed to fetch user information', 401, 'CLERK_USER_FETCH_ERROR');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching Clerk user:', error);\n    throw new APIError('Authentication service unavailable', 503, 'AUTH_SERVICE_ERROR');\n  }\n}\n\n// Utility to make backend requests with comprehensive error handling\nasync function makeBackendRequest(url: string, options: RequestInit, userId: string, userEmail: string) {\n  try {\n    const response = await fetch(url, {\n      ...options,\n      headers: {\n        'Content-Type': 'application/json',\n        'x-clerk-user-id': userId,\n        'x-user-email': userEmail,\n        ...options.headers,\n      },\n    });\n\n    const data = await response.json();\n\n    if (!response.ok) {\n      throw new APIError(\n        data.error || `Backend request failed: ${response.status}`,\n        response.status,\n        data.code || 'BACKEND_ERROR',\n        data\n      );\n    }\n\n    return data;\n  } catch (error) {\n    if (error instanceof APIError) {\n      throw error;\n    }\n\n    console.error('Backend request error:', error);\n    throw new APIError('Backend service unavailable', 503, 'BACKEND_SERVICE_ERROR');\n  }\n}\n\n/**\n * GET /api/bills - Proxy to backend bills API with enhanced validation and error handling\n */\nexport async function GET(request: NextRequest) {\n  try {\n    const { userId } = await auth();\n\n    if (!userId) {\n      auditLogger.logFinancialOperation(\n        'anonymous',\n        'anonymous',\n        'GET_BILLS_UNAUTHORIZED',\n        'bills',\n        { endpoint: '/api/bills' },\n        false,\n        'Authentication required',\n        request\n      );\n\n      return NextResponse.json(\n        {\n          error: 'Authentication required',\n          code: 'AUTH_REQUIRED',\n          message: '请先登录以访问账单数据'\n        },\n        { status: 401 }\n      );\n    }\n\n    // Check rate limiting\n    const rateLimitResult = rateLimiter.checkRateLimit(userId);\n    if (!rateLimitResult.allowed) {\n      auditLogger.logFinancialOperation(\n        userId,\n        'unknown',\n        'GET_BILLS_RATE_LIMITED',\n        'bills',\n        { endpoint: '/api/bills', resetTime: rateLimitResult.resetTime },\n        false,\n        'Rate limit exceeded',\n        request\n      );\n\n      return NextResponse.json(\n        {\n          error: 'Rate limit exceeded',\n          code: 'RATE_LIMIT_EXCEEDED',\n          message: '请求过于频繁，请稍后重试',\n          resetTime: rateLimitResult.resetTime\n        },\n        { status: 429 }\n      );\n    }\n\n    // Validate query parameters\n    const url = new URL(request.url);\n    const queryParams = Object.fromEntries(url.searchParams.entries());\n\n    // Basic query parameter validation\n    if (queryParams.limit && (isNaN(Number(queryParams.limit)) || Number(queryParams.limit) > 100)) {\n      return NextResponse.json(\n        {\n          error: 'Invalid limit parameter',\n          code: 'INVALID_LIMIT',\n          message: '分页限制必须是1-100之间的数字'\n        },\n        { status: 400 }\n      );\n    }\n\n    if (queryParams.page && (isNaN(Number(queryParams.page)) || Number(queryParams.page) < 1)) {\n      return NextResponse.json(\n        {\n          error: 'Invalid page parameter',\n          code: 'INVALID_PAGE',\n          message: '页码必须是大于0的数字'\n        },\n        { status: 400 }\n      );\n    }\n\n    // Get user info from Clerk with error handling\n    const user = await getClerkUserInfo(userId);\n    const userEmail = user.email_addresses[0]?.email_address || '';\n\n    if (!userEmail) {\n      return NextResponse.json(\n        {\n          error: 'User email not found',\n          code: 'USER_EMAIL_MISSING',\n          message: '用户邮箱信息缺失，请联系管理员'\n        },\n        { status: 400 }\n      );\n    }\n\n    // Forward request to backend with authentication headers\n    const backendUrl = `${BACKEND_URL}/api/bills${url.search}`;\n    const data = await makeBackendRequest(backendUrl, { method: 'GET' }, userId, userEmail);\n\n    // Log successful operation\n    auditLogger.logFinancialOperation(\n      userId,\n      userEmail,\n      'GET_BILLS',\n      'bills',\n      {\n        endpoint: '/api/bills',\n        queryParams: Object.fromEntries(url.searchParams.entries()),\n        resultCount: data.docs?.length || 0\n      },\n      true,\n      undefined,\n      request\n    );\n\n    return NextResponse.json(data);\n  } catch (error) {\n    if (error instanceof APIError) {\n      return NextResponse.json(\n        {\n          error: error.message,\n          code: error.code,\n          message: error.message\n        },\n        { status: error.status }\n      );\n    }\n\n    console.error('Unexpected error in GET /api/bills:', error);\n    return NextResponse.json(\n      {\n        error: 'Internal server error',\n        code: 'INTERNAL_ERROR',\n        message: '服务器内部错误，请稍后重试'\n      },\n      { status: 500 }\n    );\n  }\n}\n\n/**\n * POST /api/bills - Create new bill with comprehensive validation and error handling\n */\nexport async function POST(request: NextRequest) {\n  try {\n    const { userId } = await auth();\n\n    if (!userId) {\n      return NextResponse.json(\n        {\n          error: 'Authentication required',\n          code: 'AUTH_REQUIRED',\n          message: '请先登录以创建账单'\n        },\n        { status: 401 }\n      );\n    }\n\n    // Parse and validate request body\n    let body;\n    try {\n      body = await request.json();\n    } catch (error) {\n      return NextResponse.json(\n        {\n          error: 'Invalid JSON in request body',\n          code: 'INVALID_JSON',\n          message: '请求数据格式错误'\n        },\n        { status: 400 }\n      );\n    }\n\n    // Sanitize input data\n    try {\n      if (body.description) {\n        body.description = InputSanitizer.sanitizeText(body.description, 500);\n      }\n      if (body.notes) {\n        body.notes = InputSanitizer.sanitizeText(body.notes, 1000);\n      }\n      if (body.items) {\n        body.items = body.items.map((item: any) => ({\n          ...item,\n          itemName: InputSanitizer.sanitizeText(item.itemName, 200),\n          quantity: InputSanitizer.sanitizeAmount(item.quantity),\n          unitPrice: InputSanitizer.sanitizeAmount(item.unitPrice),\n        }));\n      }\n    } catch (sanitizationError) {\n      auditLogger.logFinancialOperation(\n        userId,\n        userEmail,\n        'CREATE_BILL_SANITIZATION_FAILED',\n        'bills',\n        { error: sanitizationError },\n        false,\n        sanitizationError instanceof Error ? sanitizationError.message : 'Sanitization failed',\n        request\n      );\n\n      return NextResponse.json(\n        {\n          error: 'Input sanitization failed',\n          code: 'SANITIZATION_ERROR',\n          message: '输入数据格式不正确'\n        },\n        { status: 400 }\n      );\n    }\n\n    // Validate bill data using Zod schema\n    const validationResult = billFormSchema.safeParse(body);\n    if (!validationResult.success) {\n      const formattedErrors = formatValidationErrors(validationResult.error);\n\n      auditLogger.logFinancialOperation(\n        userId,\n        userEmail,\n        'CREATE_BILL_VALIDATION_FAILED',\n        'bills',\n        { validationErrors: formattedErrors },\n        false,\n        'Validation failed',\n        request\n      );\n\n      return NextResponse.json(\n        {\n          error: 'Validation failed',\n          code: 'VALIDATION_ERROR',\n          message: '账单数据验证失败',\n          details: formattedErrors\n        },\n        { status: 400 }\n      );\n    }\n\n    // Get user info from Clerk with error handling\n    const user = await getClerkUserInfo(userId);\n    const userEmail = user.email_addresses[0]?.email_address || '';\n\n    if (!userEmail) {\n      return NextResponse.json(\n        {\n          error: 'User email not found',\n          code: 'USER_EMAIL_MISSING',\n          message: '用户邮箱信息缺失，请联系管理员'\n        },\n        { status: 400 }\n      );\n    }\n\n    // Add additional server-side business logic validation\n    const validatedData = validationResult.data;\n\n    // Validate bill items total matches subtotal\n    const itemsTotal = validatedData.items.reduce((sum, item) => {\n      const itemTotal = item.quantity * item.unitPrice;\n      const itemDiscount = itemTotal * ((item.discountRate || 0) / 100);\n      return sum + (itemTotal - itemDiscount);\n    }, 0);\n\n    const calculatedSubtotal = Math.round(itemsTotal * 100) / 100;\n    const providedSubtotal = Math.round((validatedData.subtotal || itemsTotal) * 100) / 100;\n\n    if (Math.abs(calculatedSubtotal - providedSubtotal) > 0.01) {\n      return NextResponse.json(\n        {\n          error: 'Subtotal mismatch',\n          code: 'SUBTOTAL_MISMATCH',\n          message: `计算的小计金额 (¥${calculatedSubtotal}) 与提供的小计金额 (¥${providedSubtotal}) 不匹配`\n        },\n        { status: 400 }\n      );\n    }\n\n    // Forward request to backend with authentication headers\n    const backendUrl = `${BACKEND_URL}/api/bills`;\n    const data = await makeBackendRequest(\n      backendUrl,\n      {\n        method: 'POST',\n        body: JSON.stringify(validatedData)\n      },\n      userId,\n      userEmail\n    );\n\n    // Log successful bill creation\n    auditLogger.logFinancialOperation(\n      userId,\n      userEmail,\n      'CREATE_BILL',\n      'bills',\n      {\n        billId: data.id,\n        patientId: validatedData.patient,\n        billType: validatedData.billType,\n        subtotal: validatedData.subtotal,\n        totalAmount: validatedData.totalAmount,\n        itemCount: validatedData.items?.length || 0\n      },\n      true,\n      undefined,\n      request\n    );\n\n    return NextResponse.json(data, { status: 201 });\n  } catch (error) {\n    if (error instanceof APIError) {\n      return NextResponse.json(\n        {\n          error: error.message,\n          code: error.code,\n          message: error.message,\n          details: error.details\n        },\n        { status: error.status }\n      );\n    }\n\n    console.error('Unexpected error in POST /api/bills:', error);\n    return NextResponse.json(\n      {\n        error: 'Internal server error',\n        code: 'INTERNAL_ERROR',\n        message: '创建账单时发生服务器错误，请稍后重试'\n      },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,cAAc,6DAAmC;AAEvD,kCAAkC;AAClC,MAAM,iBAAiB;;;;IACrB,YACE,OAAe,EACf,AAAO,SAAiB,GAAG,EAC3B,AAAO,IAAa,EACpB,AAAO,OAAa,CACpB;QACA,KAAK,CAAC,eAJC,SAAA,aACA,OAAA,WACA,UAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEA,0DAA0D;AAC1D,eAAe,iBAAiB,MAAc;IAC5C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,CAAC,+BAA+B,EAAE,QAAQ,EAAE;YACvE,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,gBAAgB,EAAE;YACzD;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,SAAS,oCAAoC,KAAK;QAC9D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM,IAAI,SAAS,sCAAsC,KAAK;IAChE;AACF;AAEA,qEAAqE;AACrE,eAAe,mBAAmB,GAAW,EAAE,OAAoB,EAAE,MAAc,EAAE,SAAiB;IACpG,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,GAAG,OAAO;YACV,SAAS;gBACP,gBAAgB;gBAChB,mBAAmB;gBACnB,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACpB;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,SACR,KAAK,KAAK,IAAI,CAAC,wBAAwB,EAAE,SAAS,MAAM,EAAE,EAC1D,SAAS,MAAM,EACf,KAAK,IAAI,IAAI,iBACb;QAEJ;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,UAAU;YAC7B,MAAM;QACR;QAEA,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM,IAAI,SAAS,+BAA+B,KAAK;IACzD;AACF;AAKO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAA,GAAA,+RAAA,CAAA,OAAI,AAAD;QAE5B,IAAI,CAAC,QAAQ;YACX,mIAAA,CAAA,cAAW,CAAC,qBAAqB,CAC/B,aACA,aACA,0BACA,SACA;gBAAE,UAAU;YAAa,GACzB,OACA,2BACA;YAGF,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,MAAM;gBACN,SAAS;YACX,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,sBAAsB;QACtB,MAAM,kBAAkB,mIAAA,CAAA,cAAW,CAAC,cAAc,CAAC;QACnD,IAAI,CAAC,gBAAgB,OAAO,EAAE;YAC5B,mIAAA,CAAA,cAAW,CAAC,qBAAqB,CAC/B,QACA,WACA,0BACA,SACA;gBAAE,UAAU;gBAAc,WAAW,gBAAgB,SAAS;YAAC,GAC/D,OACA,uBACA;YAGF,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,MAAM;gBACN,SAAS;gBACT,WAAW,gBAAgB,SAAS;YACtC,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,4BAA4B;QAC5B,MAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;QAC/B,MAAM,cAAc,OAAO,WAAW,CAAC,IAAI,YAAY,CAAC,OAAO;QAE/D,mCAAmC;QACnC,IAAI,YAAY,KAAK,IAAI,CAAC,MAAM,OAAO,YAAY,KAAK,MAAM,OAAO,YAAY,KAAK,IAAI,GAAG,GAAG;YAC9F,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,MAAM;gBACN,SAAS;YACX,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,YAAY,IAAI,IAAI,CAAC,MAAM,OAAO,YAAY,IAAI,MAAM,OAAO,YAAY,IAAI,IAAI,CAAC,GAAG;YACzF,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,MAAM;gBACN,SAAS;YACX,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,+CAA+C;QAC/C,MAAM,OAAO,MAAM,iBAAiB;QACpC,MAAM,YAAY,KAAK,eAAe,CAAC,EAAE,EAAE,iBAAiB;QAE5D,IAAI,CAAC,WAAW;YACd,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,MAAM;gBACN,SAAS;YACX,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,yDAAyD;QACzD,MAAM,aAAa,GAAG,YAAY,UAAU,EAAE,IAAI,MAAM,EAAE;QAC1D,MAAM,OAAO,MAAM,mBAAmB,YAAY;YAAE,QAAQ;QAAM,GAAG,QAAQ;QAE7E,2BAA2B;QAC3B,mIAAA,CAAA,cAAW,CAAC,qBAAqB,CAC/B,QACA,WACA,aACA,SACA;YACE,UAAU;YACV,aAAa,OAAO,WAAW,CAAC,IAAI,YAAY,CAAC,OAAO;YACxD,aAAa,KAAK,IAAI,EAAE,UAAU;QACpC,GACA,MACA,WACA;QAGF,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,UAAU;YAC7B,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO,MAAM,OAAO;gBACpB,MAAM,MAAM,IAAI;gBAChB,SAAS,MAAM,OAAO;YACxB,GACA;gBAAE,QAAQ,MAAM,MAAM;YAAC;QAE3B;QAEA,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,OAAO;YACP,MAAM;YACN,SAAS;QACX,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAKO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAA,GAAA,+RAAA,CAAA,OAAI,AAAD;QAE5B,IAAI,CAAC,QAAQ;YACX,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,MAAM;gBACN,SAAS;YACX,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,kCAAkC;QAClC,IAAI;QACJ,IAAI;YACF,OAAO,MAAM,QAAQ,IAAI;QAC3B,EAAE,OAAO,OAAO;YACd,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,MAAM;gBACN,SAAS;YACX,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,sBAAsB;QACtB,IAAI;YACF,IAAI,KAAK,WAAW,EAAE;gBACpB,KAAK,WAAW,GAAG,mIAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,KAAK,WAAW,EAAE;YACnE;YACA,IAAI,KAAK,KAAK,EAAE;gBACd,KAAK,KAAK,GAAG,mIAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,KAAK,KAAK,EAAE;YACvD;YACA,IAAI,KAAK,KAAK,EAAE;gBACd,KAAK,KAAK,GAAG,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,OAAc,CAAC;wBAC1C,GAAG,IAAI;wBACP,UAAU,mIAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,KAAK,QAAQ,EAAE;wBACrD,UAAU,mIAAA,CAAA,iBAAc,CAAC,cAAc,CAAC,KAAK,QAAQ;wBACrD,WAAW,mIAAA,CAAA,iBAAc,CAAC,cAAc,CAAC,KAAK,SAAS;oBACzD,CAAC;YACH;QACF,EAAE,OAAO,mBAAmB;YAC1B,mIAAA,CAAA,cAAW,CAAC,qBAAqB,CAC/B,QACA,WACA,mCACA,SACA;gBAAE,OAAO;YAAkB,GAC3B,OACA,6BAA6B,QAAQ,kBAAkB,OAAO,GAAG,uBACjE;YAGF,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,MAAM;gBACN,SAAS;YACX,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,sCAAsC;QACtC,MAAM,mBAAmB,gJAAA,CAAA,iBAAc,CAAC,SAAS,CAAC;QAClD,IAAI,CAAC,iBAAiB,OAAO,EAAE;YAC7B,MAAM,kBAAkB,CAAA,GAAA,gJAAA,CAAA,yBAAsB,AAAD,EAAE,iBAAiB,KAAK;YAErE,mIAAA,CAAA,cAAW,CAAC,qBAAqB,CAC/B,QACA,WACA,iCACA,SACA;gBAAE,kBAAkB;YAAgB,GACpC,OACA,qBACA;YAGF,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,MAAM;gBACN,SAAS;gBACT,SAAS;YACX,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,+CAA+C;QAC/C,MAAM,OAAO,MAAM,iBAAiB;QACpC,MAAM,YAAY,KAAK,eAAe,CAAC,EAAE,EAAE,iBAAiB;QAE5D,IAAI,CAAC,WAAW;YACd,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,MAAM;gBACN,SAAS;YACX,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,uDAAuD;QACvD,MAAM,gBAAgB,iBAAiB,IAAI;QAE3C,6CAA6C;QAC7C,MAAM,aAAa,cAAc,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK;YAClD,MAAM,YAAY,KAAK,QAAQ,GAAG,KAAK,SAAS;YAChD,MAAM,eAAe,YAAY,CAAC,CAAC,KAAK,YAAY,IAAI,CAAC,IAAI,GAAG;YAChE,OAAO,MAAM,CAAC,YAAY,YAAY;QACxC,GAAG;QAEH,MAAM,qBAAqB,KAAK,KAAK,CAAC,aAAa,OAAO;QAC1D,MAAM,mBAAmB,KAAK,KAAK,CAAC,CAAC,cAAc,QAAQ,IAAI,UAAU,IAAI,OAAO;QAEpF,IAAI,KAAK,GAAG,CAAC,qBAAqB,oBAAoB,MAAM;YAC1D,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,MAAM;gBACN,SAAS,CAAC,UAAU,EAAE,mBAAmB,aAAa,EAAE,iBAAiB,KAAK,CAAC;YACjF,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,yDAAyD;QACzD,MAAM,aAAa,GAAG,YAAY,UAAU,CAAC;QAC7C,MAAM,OAAO,MAAM,mBACjB,YACA;YACE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB,GACA,QACA;QAGF,+BAA+B;QAC/B,mIAAA,CAAA,cAAW,CAAC,qBAAqB,CAC/B,QACA,WACA,eACA,SACA;YACE,QAAQ,KAAK,EAAE;YACf,WAAW,cAAc,OAAO;YAChC,UAAU,cAAc,QAAQ;YAChC,UAAU,cAAc,QAAQ;YAChC,aAAa,cAAc,WAAW;YACtC,WAAW,cAAc,KAAK,EAAE,UAAU;QAC5C,GACA,MACA,WACA;QAGF,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;YAAE,QAAQ;QAAI;IAC/C,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,UAAU;YAC7B,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO,MAAM,OAAO;gBACpB,MAAM,MAAM,IAAI;gBAChB,SAAS,MAAM,OAAO;gBACtB,SAAS,MAAM,OAAO;YACxB,GACA;gBAAE,QAAQ,MAAM,MAAM;YAAC;QAE3B;QAEA,QAAQ,KAAK,CAAC,wCAAwC;QACtD,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,OAAO;YACP,MAAM;YACN,SAAS;QACX,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}